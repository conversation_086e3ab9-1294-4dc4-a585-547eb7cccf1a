name: Service Build

on:
  workflow_dispatch:
  push:
    # branches:
      # - main
    # tags:
    #   - '*'

env:
  REGISTRY_URL: 'funstudio.io'
  IMAGE_REPOSITORY: 'player-service'

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Cache Docker layers
        uses: actions/cache@v4
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-

      - name: Update version and tag
        id: set_tag
        run: |
          if [[ $GITHUB_REF == refs/tags/* ]]; then
            TAG=${GITHUB_REF#refs/tags/}
          else
            BRANCH_NAME=${GITHUB_REF_NAME}
            if [[ $BRANCH_NAME == */* ]]; then
              BRANCH_NAME=${BRANCH_NAME##*/}
            fi
            TAG=${<PERSON>ANCH_NAME}.snapshot.${GITHUB_RUN_NUMBER}
          fi
          echo "tag=$TAG" >> $GITHUB_OUTPUT
          echo "::notice::Building image $REGISTRY_URL/$IMAGE_REPOSITORY:$TAG"

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          push: false
          tags: |
            ${{ env.REGISTRY_URL }}/${{ env.IMAGE_REPOSITORY }}:${{ steps.set_tag.outputs.tag }}
          build-args: |
            COMMIT_ID=${{ github.sha }}
          cache-from: type=local,src=/tmp/.buildx-cache
          cache-to: type=local,dest=/tmp/.buildx-cache,mode=max
