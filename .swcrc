{"$schema": "https://json.schemastore.org/swcrc", "exclude": "node_modules/", "sourceMaps": true, "isModule": true, "module": {"type": "nodenext", "resolveFully": true}, "jsc": {"parser": {"syntax": "typescript", "decorators": true, "dynamicImport": true, "dts": true}, "target": "esnext", "baseUrl": "./src", "paths": {"@common/*": ["common/*"], "@constants/*": ["constants/*"], "@decorators/*": ["decorators/*"], "@modules/*": ["modules/*"], "@shared/*": ["shared/*"]}, "experimental": {"plugins": [["@swc/plugin-transform-imports", {"^(.*?)(\\.ts)$": {"skipDefaultConversion": true, "transform": "{{matches.[1]}}.js"}}]]}}}