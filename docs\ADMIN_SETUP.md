# Admin Account Setup Guide

This guide explains how to create admin accounts for the FS Player Service.

## 🚀 Quick Start

### Create Admin Account via <PERSON><PERSON><PERSON>

```bash
# Test database connection first
yarn create-admin --test-connection

# Run the interactive admin creation script
yarn create-admin
```

### Example Session

```
🚀 Starting admin account creation...

✅ Database connected successfully
📧 Enter admin email: <EMAIL>
👤 Enter admin full name: System Administrator
🔑 Generate random password? (y/n): y
🎲 Generated password: Kx9#mP2$vL8Q
📱 Enter admin phone (optional, press Enter to skip): +**********

✅ Admin account created successfully!
📧 Email: <EMAIL>
🔑 Password: Kx9#mP2$vL8Q
👤 Name: System Administrator
🆔 User ID: 123
🔐 Role: ADMIN (Note: Role is handled in JWT, not stored in database)
📱 Phone: +**********

🎉 Admin account is ready to use!
💡 You can now login with these credentials.
⚠️  Note: Make sure to update your authentication logic to recognize this user as admin.
```

## 🔧 Configuration

### Environment Variables

The script automatically loads environment variables from the `.env` file in the project root:

```bash
# Database configuration in .env file
DB_HOST=127.0.0.1         # Database host
DB_PORT=5432              # Database port
DB_USERNAME=fshome        # Database username
DB_PASSWORD=fspassword    # Database password
DB_DATABASE=nest_boilerplate  # Database name
```

**Note**: The script will show the loaded configuration when you run it, so you can verify the correct values are being used.

### Prerequisites

1. **Database Running**: Ensure PostgreSQL is running and accessible
2. **Database Schema**: Run migrations to create required tables
3. **Node.js**: Node.js 18+ with TypeScript support

## 🔒 Security Features

### Password Generation

- **Automatic Generation**: Option to generate secure 12-character passwords
- **Character Set**: Includes uppercase, lowercase, numbers, and special characters
- **Manual Entry**: Option to enter custom password

### Validation

- **Email Format**: Validates email format using regex
- **Duplicate Check**: Prevents creating accounts with existing emails
- **Required Fields**: Ensures all required fields are provided

### Account Properties

```typescript
{
    email: string,           // Unique email address
    username: string,        // Same as email for admin accounts
    passwordHash: string,    // Bcrypt hashed password
    accountType: 'local',    // Local account type
    status: 'active',        // Active status
    createdAtIp: '127.0.0.1' // CLI creation IP
}
```

## 🔍 Verification

### 1. Database Check

Verify the account was created in the database:

```sql
-- Check user account
SELECT user_id, email, username, account_type, status 
FROM user_account 
WHERE email = '<EMAIL>';

-- Check user profile
SELECT up.display_name, up.phone 
FROM user_profile up
JOIN user_account ua ON up.user_id = ua.user_id
WHERE ua.email = '<EMAIL>';
```

### 2. Login Test

Test login via API:

```bash
curl -X POST http://localhost:4001/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "your_password"
  }'
```

### 3. Admin Access Test

Test admin-only endpoints:

```bash
# Get JWT token from login response
TOKEN="your_jwt_token"

# Test admin-only endpoint
curl -X GET http://localhost:4001/sys-config \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json"
```

## ⚠️ Important Notes

### Role Handling

- **JWT-Based**: Admin role is determined in JWT token creation, not stored in database
- **Authentication Logic**: Update `AuthService.createToken()` to recognize admin users
- **Role Assignment**: Implement logic to assign `RoleType.ADMIN` to specific users

### Security Considerations

1. **Password Storage**: Passwords are hashed using bcrypt with salt rounds
2. **Email Uniqueness**: Email addresses must be unique across the system
3. **Account Status**: Accounts are created with 'active' status
4. **IP Tracking**: Creation IP is logged for audit purposes

### Database Schema

The script directly interacts with these tables:
- `user_account` - Main user account information
- `user_profile` - User profile details

## 🛠️ Troubleshooting

### Common Issues

1. **Database Connection Failed**
   ```
   Solution: Check database credentials and ensure PostgreSQL is running
   ```

2. **Email Already Exists**
   ```
   Solution: Use a different email address or check existing accounts
   ```

3. **Permission Denied**
   ```
   Solution: Ensure database user has INSERT permissions
   ```

### Debug Mode

Add debug logging by setting environment variable:

```bash
DEBUG=true npx tsx src/scripts/create-admin.ts
```

## 📚 Related Documentation

- [Authentication Guide](../src/modules/auth/README.md)
- [User Management Guide](../src/modules/user/README.md)
- [API Documentation](http://localhost:4001/documentation)
- [System Configuration](../src/modules/sys-config/README.md)

---

**Created with ❤️ for secure admin account management**
