# ESLint Standalone Configuration

## 📋 Overview

This project uses a **standalone ESLint configuration** that doesn't depend on Prettier plugins. All code formatting and linting rules are handled directly by ESLint using the `@stylistic/eslint-plugin`.

## 🎯 Benefits

### ✅ **Environment Independence**
- Works consistently across all IDEs and environments
- No dependency on Prettier plugins or extensions
- Consistent formatting regardless of developer setup

### ✅ **Single Tool Approach**
- ESLint handles both linting and formatting
- Simplified toolchain with fewer dependencies
- Faster execution (no need to run multiple tools)

### ✅ **Consistent Rules**
- All formatting rules defined in ESLint config
- No conflicts between Prettier and ESLint rules
- Predictable behavior across different environments

## 🔧 Configuration

### Core Dependencies
```json
{
  "@stylistic/eslint-plugin": "4.0.1",
  "eslint": "9.18.0",
  "typescript-eslint": "8.32.1"
}
```

### Key Features
- **TypeScript support** with `typescript-eslint`
- **Import sorting** with `eslint-plugin-simple-import-sort`
- **Code formatting** with `@stylistic/eslint-plugin`
- **Consistent indentation** using tabs
- **Single quotes** for strings
- **Trailing commas** for multiline structures

## 📝 Formatting Rules

### Indentation & Spacing
```typescript
'@stylistic/indent': ['error', 'tab'],
'@stylistic/space-before-function-paren': ['error', 'never'],
'@stylistic/space-in-parens': ['error', 'never'],
'@stylistic/keyword-spacing': ['error', { before: true, after: true }],
```

### Quotes & Punctuation
```typescript
'@stylistic/quotes': ['error', 'single'],
'@stylistic/semi': ['error', 'always'],
'@stylistic/comma-dangle': ['error', 'always-multiline'],
```

### Object & Array Formatting
```typescript
'@stylistic/object-curly-spacing': ['error', 'always'],
'@stylistic/array-bracket-spacing': ['error', 'never'],
'@stylistic/comma-spacing': ['error', { before: false, after: true }],
```

## 🚀 Usage

### Command Line
```bash
# Check for linting issues
yarn lint

# Auto-fix linting and formatting issues
yarn lint:fix

# Alternative formatting command
yarn format

# Check formatting without fixing
yarn format:check
```

### IDE Integration

#### VS Code
Add to `.vscode/settings.json`:
```json
{
  "eslint.enable": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "editor.formatOnSave": false,
  "eslint.format.enable": true
}
```

#### WebStorm/IntelliJ
1. Go to **Settings** → **Languages & Frameworks** → **JavaScript** → **Code Quality Tools** → **ESLint**
2. Enable **Automatic ESLint configuration**
3. Check **Run eslint --fix on save**

## 📊 Example Formatting

### Before ESLint Fix
```typescript
import {Injectable,BadRequestException} from '@nestjs/common';

@Injectable()
export class TestService{
constructor(private repo:Repository<any>){}

async testMethod(param1:string,param2:number):Promise<string>{
if(param1==="test"){
return "success";
}
const obj={
prop1:"value1",
prop2:"value2"
};
return result.length>0?"found":"not found";
}
}
```

### After ESLint Fix
```typescript
import { BadRequestException, Injectable } from '@nestjs/common';

@Injectable()
export class TestService {
	constructor(private repo: Repository<any>) {}

	async testMethod(param1: string, param2: number): Promise<string> {
		if (param1 === 'test') {
			return 'success';
		}
		const obj = {
			prop1: 'value1',
			prop2: 'value2',
		};
		return result.length > 0 ? 'found' : 'not found';
	}
}
```

## 🔍 Troubleshooting

### Common Issues

#### 1. **Parsing Errors**
If you see parsing errors, ensure the file is included in `tsconfig.json`:
```bash
# Check if file is in project scope
npx eslint src/your-file.ts
```

#### 2. **Rule Conflicts**
All formatting rules are handled by `@stylistic` plugin. No conflicts with Prettier.

#### 3. **IDE Not Applying Rules**
- Restart IDE after config changes
- Check ESLint extension is enabled
- Verify `eslint.config.mjs` is in project root

### Performance Tips
- ESLint runs faster without Prettier integration
- Use `--fix` flag for batch formatting
- Configure IDE to run ESLint on save for real-time formatting

## 📚 Migration from Prettier

If migrating from Prettier:

1. **Remove Prettier dependencies** (optional):
   ```bash
   yarn remove prettier eslint-plugin-prettier eslint-config-prettier
   ```

2. **Update scripts** in `package.json`:
   ```json
   {
     "format": "eslint ./src/ --fix",
     "format:check": "eslint ./src/"
   }
   ```

3. **Update IDE settings** to use ESLint for formatting instead of Prettier

## 🎯 Best Practices

1. **Run ESLint before commits**:
   ```bash
   yarn lint:fix
   ```

2. **Configure pre-commit hooks**:
   ```json
   "lint-staged": {
     "*.ts": ["eslint --fix", "git add"]
   }
   ```

3. **Use consistent configuration** across all environments

4. **Regular updates** of ESLint and plugins for latest features

This standalone ESLint configuration ensures consistent code formatting and linting across all development environments without external dependencies.
