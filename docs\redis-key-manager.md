# Redis Key Manager Service

## 📋 Overview

The `RedisKeyManagerService` provides centralized Redis key management across the FS Player Service application. It standardizes key patterns, prevents key conflicts, and makes Redis operations more maintainable.

## 🎯 Benefits

### 1. **Centralized Key Management**
- All Redis keys are defined in one place
- Consistent naming patterns across modules
- Easy to update key structures

### 2. **Type Safety**
- TypeScript support for key generation
- Compile-time validation of key patterns
- Autocomplete support in IDEs

### 3. **Maintainability**
- Easy to find and update key patterns
- Prevents duplicate or conflicting keys
- Clear documentation of all key structures

### 4. **Flexibility**
- Module-specific key patterns
- Custom key generation methods
- Validation and utility functions

## 🔧 Usage Examples

### Basic Usage

```typescript
import { RedisKeyManagerService } from '@shared/services/redis-key-manager.service';

@Injectable()
export class ExampleService {
  constructor(
    private readonly redisService: RedisService,
    private readonly redisKeyManager: RedisKeyManagerService
  ) {}

  async cacheUserData(userId: number, data: any) {
    const key = this.redisKeyManager.user.profile(userId);
    await this.redisService.set(key, data, 3600);
  }
}
```

### Auth Module Keys

```typescript
// Login attempts tracking
const userKey = this.redisKeyManager.auth.loginAttempts('<EMAIL>');
// Result: "login_attempts:<EMAIL>"

const ipKey = this.redisKeyManager.auth.loginAttemptsIp('***********');
// Result: "login_attempts_ip:***********"

// Token blacklisting
const blacklistKey = this.redisKeyManager.auth.blacklistToken(token);
// Result: "blacklist_token:eyJhbGciOiJSUzI1NiIs..."

// User token caching
const cacheKey = this.redisKeyManager.auth.userToken(123);
// Result: "user_token:123"
```

### OTP Module Keys

```typescript
// OTP storage
const otpKey = this.redisKeyManager.otp.code('<EMAIL>');
// Result: "otp:<EMAIL>"

// Rate limiting
const rateLimitKey = this.redisKeyManager.otp.rateLimit('<EMAIL>');
// Result: "otp_limit:<EMAIL>"

const ipRateLimitKey = this.redisKeyManager.otp.rateLimitIp('***********');
// Result: "otp_limit_ip:***********"

// Verification attempts tracking
const verifyAttemptsKey = this.redisKeyManager.otp.verifyAttempts('<EMAIL>');
// Result: "otp_verify_attempts:<EMAIL>"

// Queue management
const queueKey = this.redisKeyManager.otp.queue();
// Result: "otp:queue"

const processingKey = this.redisKeyManager.otp.processing('job_123');
// Result: "otp:processing:job_123"

// Metrics
const sentKey = this.redisKeyManager.otp.metrics.sent('2024-01-15', 'email');
// Result: "otp:sent:2024-01-15:email"

const verifiedKey = this.redisKeyManager.otp.metrics.verified('2024-01-15', 'zalo');
// Result: "otp:verified:2024-01-15:zalo"
```

### User Module Keys

```typescript
// User profile caching
const profileKey = this.redisKeyManager.user.profile(123);
// Result: "user:123"

const cacheKey = this.redisKeyManager.user.cache(123);
// Result: "profile:123"

// Session management
const sessionKey = this.redisKeyManager.user.session('session_abc123');
// Result: "user_session:session_abc123"
```

### Sys-Config Module Keys

```typescript
// Configuration caching
const configKey = this.redisKeyManager.sysConfig.value('max_login_attempts');
// Result: "sys_config:max_login_attempts"

const categoryKey = this.redisKeyManager.sysConfig.category('security');
// Result: "sys_config_category:security"

// Cache statistics
const statsKey = this.redisKeyManager.sysConfig.stats();
// Result: "sys_config:cache_stats"
```

### General Cache Keys

```typescript
// API response caching
const apiKey = this.redisKeyManager.cache.api('/users/123');
// Result: "cache:api:/users/123"

// Temporary data
const tempKey = this.redisKeyManager.cache.temp('upload_token_abc');
// Result: "temp:upload_token_abc"

// Distributed locks
const lockKey = this.redisKeyManager.cache.lock('user_update_123');
// Result: "lock:user_update_123"
```

## 🛠️ Advanced Features

### Custom Key Generation

```typescript
// Generate custom keys with validation
const customKey = this.redisKeyManager.custom('module', 'type', 'identifier');
// Result: "module:type:identifier"

// Time-based keys for metrics
const timeKey = this.redisKeyManager.timeBasedKey('analytics', 'pageviews');
// Result: "analytics:pageviews:2024-01-15"

const specificTimeKey = this.redisKeyManager.timeBasedKey(
  'analytics', 
  'pageviews', 
  new Date('2024-01-10')
);
// Result: "analytics:pageviews:2024-01-10"
```

### Batch Operations

```typescript
// Generate multiple keys for batch operations
const userIds = [1, 2, 3, 4, 5];
const userKeys = this.redisKeyManager.batch(
  (id) => this.redisKeyManager.user.profile(id),
  userIds
);
// Result: ["user:1", "user:2", "user:3", "user:4", "user:5"]

// Use with Redis pipeline
const pipeline = this.redisService.createPipeline();
userKeys.forEach(key => pipeline.del(key));
await pipeline.exec();
```

### Key Validation

```typescript
// Validate key format
const validation = this.redisKeyManager.validateKey('user:123');
console.log(validation);
// Result: { valid: true, issues: [] }

const invalidValidation = this.redisKeyManager.validateKey('invalid key with spaces');
console.log(invalidValidation);
// Result: { valid: false, issues: ['Key should not contain spaces'] }
```

### Key Analysis

```typescript
// Extract information from keys
const key = 'otp:sent:2024-01-15:email';

const module = this.redisKeyManager.extractModule(key);
// Result: "otp"

const type = this.redisKeyManager.extractType(key);
// Result: "sent"

const identifier = this.redisKeyManager.extractIdentifier(key);
// Result: "2024-01-15:email"
```

## 📊 Key Patterns Reference

### Auth Module
| Method | Pattern | Example |
|--------|---------|---------|
| `loginAttempts(username)` | `login_attempts:{username}` | `login_attempts:<EMAIL>` |
| `loginAttemptsIp(ip)` | `login_attempts_ip:{ip}` | `login_attempts_ip:***********` |
| `blacklistToken(token)` | `blacklist_token:{token}` | `blacklist_token:eyJhbGci...` |
| `userToken(userId)` | `user_token:{userId}` | `user_token:123` |

### OTP Module
| Method | Pattern | Example |
|--------|---------|---------|
| `code(contact)` | `otp:{contact}` | `otp:<EMAIL>` |
| `rateLimit(contact)` | `otp_limit:{contact}` | `otp_limit:<EMAIL>` |
| `rateLimitIp(ip)` | `otp_limit_ip:{ip}` | `otp_limit_ip:***********` |
| `verifyAttempts(contact)` | `otp_verify_attempts:{contact}` | `otp_verify_attempts:<EMAIL>` |
| `queue()` | `otp:queue` | `otp:queue` |
| `processing(jobId)` | `otp:processing:{jobId}` | `otp:processing:job_123` |
| `failed(jobId)` | `otp:failed:{jobId}` | `otp:failed:job_123` |
| `metrics.sent(date, type)` | `otp:sent:{date}:{type}` | `otp:sent:2024-01-15:email` |
| `metrics.verified(date, type)` | `otp:verified:{date}:{type}` | `otp:verified:2024-01-15:zalo` |

### User Module
| Method | Pattern | Example |
|--------|---------|---------|
| `profile(userId)` | `user:{userId}` | `user:123` |
| `cache(userId)` | `profile:{userId}` | `profile:123` |
| `session(sessionId)` | `user_session:{sessionId}` | `user_session:abc123` |

### Sys-Config Module
| Method | Pattern | Example |
|--------|---------|---------|
| `value(key)` | `sys_config:{key}` | `sys_config:max_login_attempts` |
| `category(category)` | `sys_config_category:{category}` | `sys_config_category:security` |
| `stats()` | `sys_config:cache_stats` | `sys_config:cache_stats` |

### Cache Module
| Method | Pattern | Example |
|--------|---------|---------|
| `api(endpoint)` | `cache:api:{endpoint}` | `cache:api:/users/123` |
| `temp(key)` | `temp:{key}` | `temp:upload_token_abc` |
| `lock(resource)` | `lock:{resource}` | `lock:user_update_123` |

## 🔧 Integration

The RedisKeyManagerService is automatically available in all modules since it's registered in the global SharedModule:

```typescript
constructor(
  private readonly redisService: RedisService,
  private readonly redisKeyManager: RedisKeyManagerService
) {}
```

No additional imports or module registrations required!

## 📈 Best Practices

1. **Always use RedisKeyManagerService** instead of hardcoded strings
2. **Validate keys** in development using `validateKey()`
3. **Use batch operations** for multiple related keys
4. **Follow naming conventions** when adding new key patterns
5. **Document new patterns** when extending the service

## 🚀 Migration Guide

### Before (Hardcoded Keys)
```typescript
const userKey = `login_attempts:${username}`;
const ipKey = `login_attempts_ip:${ip}`;
const otpKey = `otp:${emailOrPhone}`;
```

### After (RedisKeyManagerService)
```typescript
const userKey = this.redisKeyManager.auth.loginAttempts(username);
const ipKey = this.redisKeyManager.auth.loginAttemptsIp(ip);
const otpKey = this.redisKeyManager.otp.code(emailOrPhone);
```

**Benefits of migration:**
- Type safety and autocomplete
- Centralized key management
- Easier refactoring and maintenance
- Consistent naming patterns
- Built-in validation
