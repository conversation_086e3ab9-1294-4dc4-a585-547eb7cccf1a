# RedisService Documentation

## Overview

The `RedisService` is a global service that provides a simple and consistent interface for interacting with Redis in your NestJS application. It automatically handles connection management, key prefixing, and provides common Redis operations.

## Features

- ✅ **Global availability** - Available throughout your entire application
- ✅ **Automatic key prefixing** - All keys are prefixed with `fsp::`
- ✅ **Connection management** - Automatic connection and cleanup
- ✅ **Type safety** - Generic support for typed data retrieval
- ✅ **Error handling** - Comprehensive error logging
- ✅ **JSON serialization** - Automatic JSON serialization/deserialization
- ✅ **TTL support** - Time-to-live support for cache expiration

## Configuration

The service uses the same Redis configuration as your existing cache setup:
- Host: `REDIS_HOST` or `INTERNAL_REDIS_HOST` (default: localhost)
- Port: `REDIS_PORT` or `INTERNAL_REDIS_PORT` (default: 6379)
- Key prefix: `fsp::`

## API Reference

### Core Methods

#### `set(key: string, value: unknown, ttlSeconds?: number): Promise<void>`
Stores a value in Redis with optional TTL.

```typescript
// Set a simple value
await redisService.set('user:123', { name: '<PERSON>', email: '<EMAIL>' });

// Set with TTL (expires in 300 seconds)
await redisService.set('session:abc', sessionData, 300);
```

#### `get<T>(key: string): Promise<T | null>`
Retrieves a value from Redis with type safety.

```typescript
// Get a typed value
const user = await redisService.get<User>('user:123');

// Get any value
const data = await redisService.get('session:abc');
```

#### `del(key: string): Promise<number>`
Deletes a key from Redis. Returns the number of keys deleted.

```typescript
const deleted = await redisService.del('user:123');
console.log(deleted > 0 ? 'Key deleted' : 'Key not found');
```

### Utility Methods

#### `exists(key: string): Promise<boolean>`
Checks if a key exists in Redis.

```typescript
const exists = await redisService.exists('user:123');
if (exists) {
  // Key exists
}
```

#### `keys(pattern: string = '*'): Promise<string[]>`
Gets all keys matching a pattern (without prefix).

```typescript
// Get all user keys
const userKeys = await redisService.keys('user:*');

// Get all keys
const allKeys = await redisService.keys();
```

#### `ping(): Promise<string>`
Pings Redis server. Returns 'PONG' if successful.

```typescript
const result = await redisService.ping();
console.log(result); // 'PONG'
```

#### `flushAll(): Promise<void>`
⚠️ **WARNING**: Clears all data from Redis. Use with caution!

```typescript
await redisService.flushAll(); // Deletes ALL data
```

#### `getClient(): RedisClientType`
Gets direct access to the Redis client for advanced operations.

```typescript
const client = redisService.getClient();
await client.publish('channel', 'message');
```

## Usage Examples

### Basic Usage

```typescript
import { Injectable } from '@nestjs/common';
import { RedisService } from '@shared/services/redis.service';

@Injectable()
export class UserService {
  constructor(private readonly redisService: RedisService) {}

  async cacheUser(userId: string, userData: User): Promise<void> {
    await this.redisService.set(`user:${userId}`, userData, 3600); // 1 hour TTL
  }

  async getUser(userId: string): Promise<User | null> {
    return await this.redisService.get<User>(`user:${userId}`);
  }
}
```

### Session Management

```typescript
async cacheUserSession(userId: string, sessionData: any): Promise<void> {
  const key = `session:${userId}`;
  await this.redisService.set(key, sessionData, 3600); // 1 hour
}

async getUserSession(userId: string): Promise<any | null> {
  return await this.redisService.get(`session:${userId}`);
}
```

### Rate Limiting

```typescript
async checkRateLimit(userId: string, maxRequests: number = 100): Promise<boolean> {
  const key = `rate_limit:${userId}`;
  const current = await this.redisService.get<number>(key) || 0;
  
  if (current >= maxRequests) {
    return false; // Rate limit exceeded
  }
  
  await this.redisService.set(key, current + 1, 3600); // 1 hour window
  return true;
}
```

## Available Endpoints

The service includes test endpoints for development:

- `GET /user/redis/set/:key/:value` - Set a value with 120s TTL
- `GET /user/redis/get/:key` - Get a value
- `DELETE /user/redis/del/:key` - Delete a key
- `GET /user/redis/exists/:key` - Check if key exists
- `GET /user/redis/keys` - Get all keys
- `GET /user/redis/keys/:pattern` - Get keys by pattern
- `POST /user/redis/ping` - Ping Redis server

## Key Prefixing

All keys are automatically prefixed with `fsp::` to avoid conflicts:

```typescript
// Your code
await redisService.set('user:123', userData);

// Actual Redis key
// fsp::user:123
```

When retrieving keys with the `keys()` method, the prefix is automatically removed from the results.

## Error Handling

The service includes comprehensive error handling and logging:

```typescript
try {
  await redisService.set('key', 'value');
} catch (error) {
  // Error is logged automatically
  // Handle the error appropriately
}
```

## Best Practices

1. **Use meaningful key patterns**: `user:123`, `session:abc`, `cache:api:endpoint`
2. **Set appropriate TTLs**: Don't let data live forever unless necessary
3. **Handle null returns**: Always check for null when getting values
4. **Use type safety**: Specify types when getting values
5. **Monitor Redis usage**: Use the ping and keys methods for health checks

## Integration

The RedisService is automatically available in all modules since it's registered in the global SharedModule. Simply inject it into any service or controller:

```typescript
constructor(private readonly redisService: RedisService) {}
```

No additional imports or module registrations required!
