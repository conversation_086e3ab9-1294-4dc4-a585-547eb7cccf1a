import path from 'node:path';

import { AuditLogModule } from '@modules/audit-log/audit-log.module';
import { SysConfigModule } from '@modules/sys-config/sys-config.module';
import { ZaloOAuthModule } from '@modules/zalo-oauth/zalo-oauth.module';
import { CacheModule } from '@nestjs/cache-manager';
import type { MiddlewareConsumer, NestModule } from '@nestjs/common';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { ServeStaticModule } from '@nestjs/serve-static';
import { ThrottlerModule } from '@nestjs/throttler';
import { TypeOrmModule } from '@nestjs/typeorm';
import { redisStore } from 'cache-manager-redis-store';
import { ResponseInterceptor } from 'interceptors/response-interceptor.service';
import { IpWhitelistMiddleware } from 'middleware/ip-whitelist.middleware';
import { ClsModule } from 'nestjs-cls';
import {
	AcceptLanguageResolver,
	HeaderResolver,
	I18nModule,
	QueryResolver,
} from 'nestjs-i18n';
import { DataSource } from 'typeorm';
import { addTransactionalDataSource } from 'typeorm-transactional';

import { JwtBlacklistMiddleware } from './middleware/jwt-blacklist.middleware';
import { AuthModule } from './modules/auth/auth.module';
import { HealthCheckerModule } from './modules/health-checker/health-checker.module';
import { UserModule } from './modules/user/user.module';
import { ApiConfigService } from './shared/services/api-config.service';
import { SharedModule } from './shared/shared.module';

@Module({
	imports: [
		ServeStaticModule.forRoot({
			rootPath: path.join(import.meta.dirname, '..', 'public'),
			serveRoot: '/',
		}),
		CacheModule.register({
			isGlobal: true,
			useFactory: async(configService: ApiConfigService) => {
				return {
					store: redisStore,
					host: configService.redisConfig.socket.host,
					port: configService.redisConfig.socket.port,
					ttl: configService.redisConfig.ttl,
				};
			},
			inject: [ApiConfigService],
		}),
		AuthModule,
		SysConfigModule,
		ZaloOAuthModule,
		UserModule,
		ClsModule.forRoot({
			global: true,
			middleware: {
				mount: true,
			},
		}),
		ThrottlerModule.forRootAsync({
			imports: [SharedModule],
			useFactory: (configService: ApiConfigService) => ({
				throttlers: [configService.throttlerConfigs],
			}),
			inject: [ApiConfigService],
		}),
		ConfigModule.forRoot({
			isGlobal: true,
			envFilePath: '.env',
		}),
		TypeOrmModule.forRootAsync({
			imports: [SharedModule],
			useFactory: (configService: ApiConfigService) =>
				configService.postgresConfig,
			inject: [ApiConfigService],
			dataSourceFactory: (options) => {
				if (!options) {
					throw new Error('Invalid options passed');
				}

				const dataSource = new DataSource(options);

				// Try to add transactional DataSource, handle duplicate error
				try {
					return Promise.resolve(addTransactionalDataSource(dataSource));
				} catch (error) {
					// If DataSource already exists, return the plain DataSource
					if (
						error instanceof Error &&
						error.message.includes('has already added')
					) {
						console.warn('DataSource already exists, using plain DataSource');
						return Promise.resolve(dataSource);
					}
					throw error;
				}
			},
		}),
		I18nModule.forRootAsync({
			useFactory: (configService: ApiConfigService) => ({
				fallbackLanguage: configService.fallbackLanguage,
				loaderOptions: {
					path: path.join(import.meta.dirname, 'i18n/'),
					watch: configService.isDevelopment,
				},
			}),
			resolvers: [
				{ use: QueryResolver, options: ['lang'] },
				AcceptLanguageResolver,
				new HeaderResolver(['x-lang']),
			],
			imports: [SharedModule],
			inject: [ApiConfigService],
		}),
		HealthCheckerModule,
		AuditLogModule,
	],
	providers: [
		IpWhitelistMiddleware,
		JwtBlacklistMiddleware,
		{
			provide: APP_INTERCEPTOR,
			useClass: ResponseInterceptor,
		},
	],
})
export class AppModule implements NestModule {
	configure(consumer: MiddlewareConsumer) {
		consumer.apply(IpWhitelistMiddleware).forRoutes('*');
		consumer
			.apply(JwtBlacklistMiddleware)
			.exclude(
				'/auth/login',
				'/auth/register',
				'/auth/contact-verify',
				'/auth/reset-password/request',
				'/auth/reset-password',
				'/auth/oauth2/*path',
				'/otp/*path',
				'/health',
				'/documentation/*path',
			)
			.forRoutes('*');
	}
}
