import {
	DateField,
	// <PERSON><PERSON><PERSON>ield,
} from '@decorators/field.decorators';
import { DYNAMIC_TRANSLATION_DECORATOR_KEY } from '@decorators/translate.decorator';

import { ContextProvider } from '../../providers/context.provider.ts';
import type { AbstractEntity } from '../abstract.entity.ts';

export class AbstractDto {
	//   @UUIDField()
	//   id!: Uuid;

	@DateField()
	createdAt!: Date;

	@DateField()
	updatedAt!: Date;

	translations?: AbstractTranslationDto[];

	constructor(entity: AbstractEntity, options?: { excludeFields?: boolean }) {
		if (!options?.excludeFields) {
			//   this.id = entity.id;
			this.createdAt = entity.createdAt;
			this.updatedAt = entity.updatedAt;
		}

		const languageCode = ContextProvider.getLanguage();

		if (languageCode && entity.translations) {
			const translationEntity = entity.translations.find(
				(titleTranslation) => titleTranslation.languageCode === languageCode,
			)!;

			const fields: Record<string, string> = {};

			for (const key of Object.keys(translationEntity)) {
				const metadata = Reflect.getMetadata(
					DYNAMIC_TRANSLATION_DECORATOR_KEY,
					this,
					key,
				);

				if (metadata) {
					fields[key] = (translationEntity as never)[key];
				}
			}

			Object.assign(this, fields);
		} else {
			this.translations = entity.translations?.toDtos();
		}
	}
}

export class AbstractTranslationDto extends AbstractDto {
	constructor(entity: AbstractEntity) {
		super(entity, { excludeFields: true });
	}
}
