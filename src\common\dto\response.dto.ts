import { ResponseCode } from '@constants/response-codes';

export class ResponseDto<T = any> {
	statusCode: ResponseCode;
	message: string;
	data: T;

	constructor(statusCode: ResponseCode, message: string, data: T) {
		this.statusCode = statusCode;
		this.message = message;
		this.data = data;
	}

	static success<T>(data: T, message?: string): ResponseDto<T> {
		return new ResponseDto(
			ResponseCode.SUCCESS,
			message || 'Operation completed successfully',
			data,
		);
	}

	static error(statusCode: ResponseCode, message: string): ResponseDto<object> {
		return new ResponseDto(statusCode, message, {});
	}
}