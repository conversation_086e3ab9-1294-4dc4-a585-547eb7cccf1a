import { UserAccountStatus } from '@constants/user';
import {
	BadRequestException,
	ConflictException,
	ForbiddenException,
	NotFoundException,
} from '@nestjs/common';

import { ErrorHelper } from '../error.helper';

describe('ErrorHelper', () => {
	describe('validateContactFormat', () => {
		it('should return "email" for valid email', () => {
			const result = ErrorHelper.validateContactFormat('<EMAIL>');
			expect(result).toBe('email');
		});

		it('should return "phone" for valid Vietnamese phone', () => {
			const result = ErrorHelper.validateContactFormat('***********');
			expect(result).toBe('phone');
		});

		it('should throw BadRequestException for empty contact', () => {
			expect(() => ErrorHelper.validateContactFormat('')).toThrow(
				BadRequestException,
			);
			expect(() => ErrorHelper.validateContactFormat('')).toThrow(
				'Contact is required.',
			);
		});

		it('should throw BadRequestException for invalid format', () => {
			expect(() => ErrorHelper.validateContactFormat('invalid')).toThrow(
				BadRequestException,
			);
			expect(() => ErrorHelper.validateContactFormat('invalid')).toThrow(
				'Must be a valid email address or Vietnamese phone number (84xxxxxxxxx)',
			);
		});
	});

	describe('validateUserAccountStatus', () => {
		it('should not throw for active user in login context', () => {
			const user = { status: UserAccountStatus.ACTIVE };
			expect(() =>
				ErrorHelper.validateUserAccountStatus(user, 'login'),
			).not.toThrow();
		});

		it('should throw NotFoundException for null user in login context', () => {
			expect(() =>
				ErrorHelper.validateUserAccountStatus(null, 'login'),
			).toThrow(NotFoundException);
			expect(() =>
				ErrorHelper.validateUserAccountStatus(null, 'login'),
			).toThrow('User not found');
		});

		it('should throw ForbiddenException for inactive user in login context', () => {
			const user = { status: UserAccountStatus.INACTIVE };
			expect(() =>
				ErrorHelper.validateUserAccountStatus(user, 'login'),
			).toThrow(ForbiddenException);
			expect(() =>
				ErrorHelper.validateUserAccountStatus(user, 'login'),
			).toThrow('Account is not active. Please contact support.');
		});

		it('should throw ConflictException for active user in registration context', () => {
			const user = { status: UserAccountStatus.ACTIVE };
			expect(() =>
				ErrorHelper.validateUserAccountStatus(user, 'registration'),
			).toThrow(ConflictException);
			expect(() =>
				ErrorHelper.validateUserAccountStatus(user, 'registration'),
			).toThrow('User with this email/phone already exists');
		});

		it('should not throw for null user in registration context', () => {
			expect(() =>
				ErrorHelper.validateUserAccountStatus(null, 'registration'),
			).not.toThrow();
		});
	});

	describe('createRateLimitError', () => {
		it('should create user rate limit error', () => {
			const error = ErrorHelper.createRateLimitError(
				'user',
				'<EMAIL>',
				15,
			);
			expect(error).toBeInstanceOf(BadRequestException);
			expect(error.message).toBe(
				'Too many <NAME_EMAIL>. Please try again in 15 minutes.',
			);
		});

		it('should create IP rate limit error', () => {
			const error = ErrorHelper.createRateLimitError('ip', '***********', 10);
			expect(error).toBeInstanceOf(BadRequestException);
			expect(error.message).toBe(
				'Too many requests from this IP. Please try again in 10 minutes.',
			);
		});
	});

	describe('createOtpError', () => {
		it('should create expired OTP error', () => {
			const error = ErrorHelper.createOtpError('expired');
			expect(error).toBeInstanceOf(BadRequestException);
			expect(error.message).toBe('OTP has expired. Please request a new one.');
		});

		it('should create invalid OTP error', () => {
			const error = ErrorHelper.createOtpError('invalid');
			expect(error).toBeInstanceOf(BadRequestException);
			expect(error.message).toBe('Invalid OTP code');
		});

		it('should create max attempts OTP error', () => {
			const error = ErrorHelper.createOtpError('max_attempts');
			expect(error).toBeInstanceOf(BadRequestException);
			expect(error.message).toBe(
				'Maximum OTP verification attempts exceeded. Please request a new OTP.',
			);
		});
	});

	describe('sanitizeErrorForLogging', () => {
		it('should sanitize password in error message', () => {
			const error = 'Login failed with password=secret123 and token=abc123';
			const sanitized = ErrorHelper.sanitizeErrorForLogging(error);
			expect(sanitized).toBe('Login failed with password=*** and token=***');
		});

		it('should handle Error objects', () => {
			const error = new Error('Database connection failed');
			const sanitized = ErrorHelper.sanitizeErrorForLogging(error);
			expect(sanitized).toBe('Database connection failed');
		});

		it('should sanitize multiple sensitive fields', () => {
			const error = 'Failed: password=secret, key=apikey123, token=jwt456';
			const sanitized = ErrorHelper.sanitizeErrorForLogging(error);
			expect(sanitized).toBe('Failed: password=***, key=***, token=***');
		});
	});
});
