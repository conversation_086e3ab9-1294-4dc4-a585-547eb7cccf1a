import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from '@shared/services/redis.service';

/**
 * Shared cache helper with consistent error handling
 */
@Injectable()
export class CacheHelper {
	private readonly logger = new Logger(CacheHelper.name);

	constructor(private readonly redisService: RedisService) {}

	/**
	 * Get from cache with fallback to database
	 */
	async getWithFallback<T>(
		cacheKey: string,
		fallbackFn: () => Promise<T>,
		ttl: number = 3600,
		context?: string,
	): Promise<T> {
		try {
			// Try cache first
			const cached = await this.redisService.get<T>(cacheKey);
			if (cached !== null && cached !== undefined) {
				return cached;
			}
		} catch (error) {
			this.logCacheError('read', cacheKey, error, context);
		}

		// Fallback to database
		const data = await fallbackFn();

		// Cache the result
		try {
			await this.redisService.set(cacheKey, data, ttl);
		} catch (error) {
			this.logCacheError('write', cacheKey, error, context);
		}

		return data;
	}

	/**
	 * Set cache with error handling
	 */
	async setWithErrorHandling<T>(
		cacheKey: string,
		data: T,
		ttl: number = 3600,
		context?: string,
	): Promise<void> {
		try {
			await this.redisService.set(cacheKey, data, ttl);
		} catch (error) {
			this.logCacheError('write', cacheKey, error, context);
		}
	}

	/**
	 * Delete cache with error handling
	 */
	async deleteWithErrorHandling(
		cacheKey: string,
		context?: string,
	): Promise<void> {
		try {
			await this.redisService.del(cacheKey);
		} catch (error) {
			this.logCacheError('delete', cacheKey, error, context);
		}
	}

	/**
	 * Batch cache operations
	 */
	async batchGet<T>(keys: string[]): Promise<(T | null)[]> {
		try {
			return await Promise.all(
				keys.map((key) => this.redisService.get<T>(key)),
			);
		} catch (error) {
			this.logger.error('Batch cache read failed:', error);
			return keys.map(() => null);
		}
	}

	/**
	 * Batch cache set
	 */
	async batchSet<T>(
		items: Array<{ key: string; value: T; ttl?: number }>,
		context?: string,
	): Promise<void> {
		try {
			await Promise.all(
				items.map(({ key, value, ttl = 3600 }) =>
					this.redisService.set(key, value, ttl),
				),
			);
		} catch (error) {
			this.logCacheError('batch_write', 'multiple_keys', error, context);
		}
	}

	/**
	 * Cache statistics tracking
	 */
	async updateCacheStats(
		hit: boolean,
		statsKey: string,
		context?: string,
	): Promise<void> {
		try {
			const field = hit ? 'hits' : 'misses';
			// Use simple increment since RedisService doesn't have hincrby
			const fieldKey = `${statsKey}:${field}`;
			const totalKey = `${statsKey}:total`;

			await this.redisService.incr(fieldKey);
			await this.redisService.incr(totalKey);
		} catch (error) {
			this.logCacheError('stats_update', statsKey, error, context);
		}
	}

	private logCacheError(
		operation: string,
		key: string,
		error: any,
		context?: string,
	): void {
		const contextStr = context ? `[${context}] ` : '';
		this.logger.warn(
			`${contextStr}Cache ${operation} failed for key ${key}:`,
			error instanceof Error ? error.message : String(error),
		);
	}
}
