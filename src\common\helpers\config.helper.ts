import { SysConfigService } from '@modules/sys-config/sys-config.service';
import { Injectable, Logger } from '@nestjs/common';

/**
 * Shared configuration helper with fallback support
 */
@Injectable()
export class ConfigHelper {
	private readonly logger = new Logger(ConfigHelper.name);

	constructor(private readonly sysConfigService: SysConfigService) {}

	/**
	 * Get configuration value with fallback to default
	 * @param key Configuration key
	 * @param defaultValue Default value if config not found
	 * @param context Context for logging (e.g., 'auth', 'otp')
	 */
	async getConfigWithFallback<T>(
		key: string,
		defaultValue: T,
		context?: string,
	): Promise<T> {
		try {
			return await this.sysConfigService.getValue<T>(key);
		} catch (error) {
			const contextStr = context ? `[${context}] ` : '';
			this.logger.warn(
				`${contextStr}Failed to get config for key ${key}, using default:`,
				error instanceof Error ? error.message : String(error),
			);
			return defaultValue;
		}
	}

	/**
	 * Get multiple configs with fallbacks
	 */
	async getMultipleConfigs<T extends Record<string, any>>(
		configs: Array<{ key: string; defaultValue: any; context?: string }>,
	): Promise<T> {
		const results = await Promise.allSettled(
			configs.map(async({ key, defaultValue, context }) => ({
				key,
				value: await this.getConfigWithFallback(key, defaultValue, context),
			})),
		);

		const configMap = {} as T;
		results.forEach((result, index) => {
			const config = configs[index];
			if (!config) return;

			if (result.status === 'fulfilled') {
				const configKey = config.key;
				configMap[configKey as keyof T] = result.value.value;
			} else {
				const { key, defaultValue } = config;
				configMap[key as keyof T] = defaultValue;
				this.logger.error(`Failed to get config ${key}:`, result.reason);
			}
		});

		return configMap;
	}
}
