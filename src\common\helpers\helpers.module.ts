import { SysConfigModule } from '@modules/sys-config/sys-config.module';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { SysConfigEntity } from '../../modules/sys-config/entities/sys-config.entity';
import { SysConfigService } from '../../modules/sys-config/sys-config.service';
import { RedisService } from '../../shared/services/redis.service';
import { CacheHelper } from './cache.helper';
import { ConfigHelper } from './config.helper';

/**
 * Common helpers module
 * Provides shared utilities across the application
 */
@Module({
	imports: [
		SysConfigModule, // For SysConfigService
		TypeOrmModule.forFeature([SysConfigEntity]),
	],
	providers: [ConfigHelper, CacheHelper, RedisService, SysConfigService],
	exports: [ConfigHelper, CacheHelper, RedisService, SysConfigService],
})
export class HelpersModule {}
