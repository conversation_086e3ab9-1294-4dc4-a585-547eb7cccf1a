import { RoleType } from '@constants/role-type';
import { TokenType } from '@constants/token-type';
import type { JwtService } from '@nestjs/jwt';
import bcrypt from 'bcrypt';
import { UtilException } from 'exceptions';
import type { Request } from 'express';

/**
 * generate hash from password or string
 * @param {string} password
 * @returns {string}
 */
export function generateHash(password: string): string {
	return bcrypt.hashSync(password, 10);
}

/**
 * validate text with hash
 * @param {string} password
 * @param {string} hash
 * @returns {Promise<boolean>}
 */
export function validateHash(
	password: string | undefined,
	hash: string | undefined | null,
): Promise<boolean> {
	if (!password || !hash) {
		return Promise.resolve(false);
	}

	return bcrypt.compare(password, hash);
}

export function getVariableName<TResult>(
	getVar: () => TResult,
): string | undefined {
	const m = /\(\)=>(.*)/.exec(
		getVar.toString().replaceAll(/(\r\n|\n|\r|\s)/gm, ''),
	);

	if (!m) {
		throw new UtilException(
			'The function does not contain a statement matching \'return variableName;\'',
		);
	}

	const fullMemberName = m[1]!;

	const memberParts = fullMemberName.split('.');

	return memberParts.at(-1);
}

export function getIp(request: Request): string {
	// Cloudflare header
	const cfIp = request.headers['cf-connecting-ip'];
	if (typeof cfIp === 'string') {
		return cfIp;
	}

	let ip = '127.0.0.1';

	// Standard proxy header
	const forwarded = request.headers['x-forwarded-for'];
	if (forwarded !== undefined) {
		if (typeof forwarded === 'string') {
			ip = (forwarded.split(',')[0] || ip).trim();
		} else if (Array.isArray(forwarded) && forwarded.length > 0) {
			ip = forwarded[0] || ip;
		}
	} else {
		// Express/Socket IP
		ip = request.ip || request.socket?.remoteAddress || ip;
	}
	// Normalize IPv6 loopback
	if (ip === '::1' || ip === '0:0:0:0:0:0:0:1') {
		ip = '127.0.0.1';
	}

	// If IPv6 mapped IPv4 (::ffff:127.0.0.1) → extract IPv4
	if (ip.startsWith('::ffff:')) {
		ip = ip.substring(7);
	}

	return ip;
}

export function isEmail(value: string): boolean {
	return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
}

export function isVietnamesePhone(value: string): boolean {
	return /^84[0-9]{9}$|^84[0-9]{10}$/.test(value);
}

/**
 * Determine contact type from value
 */
export function getContactType(value: string): 'email' | 'phone' | 'username' {
	if (isEmail(value)) return 'email';
	if (isVietnamesePhone(value)) return 'phone';
	return 'username';
}

/**
 * Mask một chuỗi ID/number, chừa lại một phần đầu và cuối.
 * 
 * @param value - ID cần mask (string/number).
 * @param visibleStart - số ký tự muốn chừa ở đầu (default = 0).
 * @param visibleEnd - số ký tự muốn chừa ở cuối (default = 4).
 * @param maskChar - ký tự dùng để mask (default = '*').
 */
export function maskId(
	value: string | number,
	visibleStart: number = 0,
	visibleEnd: number = 4,
	maskChar: string = '*',
): string {
	if (!value) return '';

	const str = String(value);
	const length = str.length;

	// nếu tổng visible vượt quá độ dài thì không cần mask
	if (visibleStart + visibleEnd >= length) {
		return str;
	}

	const start = str.slice(0, visibleStart);
	const end = str.slice(length - visibleEnd);
	const masked = maskChar.repeat(length - visibleStart - visibleEnd);

	return `${start}${masked}${end}`;
}

/**
 * Mask email: chừa lại vài ký tự đầu và cuối của username, giữ nguyên domain.
 * 
 * @param email - địa chỉ email
 * @param visibleStart - số ký tự muốn chừa ở đầu username (default = 1)
 * @param visibleEnd - số ký tự muốn chừa ở cuối username (default = 3).
 * @param maskChar - ký tự mask (default = '*')
 */
export function maskEmail(
	email: string,
	visibleStart: number = 1,
	visibleEnd: number = 3,
	maskChar: string = '*',
): string {
	if (!email || !email.includes('@')) return email;

	const [username = '', domain = ''] = email.split('@');
	if (username.length <= visibleStart) {
		return `${username}@${domain}`;
	}

	const masked = maskId(username, visibleStart, visibleEnd, maskChar);
	return `${masked}@${domain}`;
}

export async function createToken(
	jwtService: JwtService,
	userId: number,
	accessTokenExpiresIn: number = 60 * 60,
	refreshTokenExpiresIn: number = 60 * 60 * 24 * 7,
	role = RoleType.USER,
): Promise<{ 
		userId: number; 
		expiresIn: number; 
		token: string; 
		refreshToken: string 
	}> {
	const [token, refreshToken] = await Promise.all([
		jwtService.signAsync(
			{ userId: userId, type: TokenType.ACCESS_TOKEN, role },
			{ expiresIn: accessTokenExpiresIn },
		),
		jwtService.signAsync(
			{ userId: userId, type: TokenType.REFRESH_TOKEN, role },
			{ expiresIn: refreshTokenExpiresIn },
		),
	]);

	return {
		userId: userId,
		expiresIn: accessTokenExpiresIn,
		token,
		refreshToken,
	};
}

export function getLoginData(user: any, tokens: any, ip: string) {
	return {
		user: user,
		tokens,
		metadata: {
			loginAt: new Date().toISOString(),
			ipAddress: ip,
		},
	};
}