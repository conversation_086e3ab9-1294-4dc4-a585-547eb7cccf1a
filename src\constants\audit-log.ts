export enum AuditLogLevel  {
	CRITICAL = 'critical',
	ERROR = 'error',
	INFO = 'info',
	WARNING = 'warning',
}

export enum AuditLogCategory {
	AUTHENTICATION = 'authentication',
	AUTHORIZATION = 'authorization', 
	PAYMENT = 'payment',
	GAME = 'game',
	USER_MANAGEMENT = 'user_management',
	SYSTEM = 'system',
	SECURITY = 'security',
	API = 'api',
}


export enum AuditLogAction  {
	LOGIN_SUCCESS = 'login_success',
	LOGIN_FAILED_INVALID_CREDENTIALS = 'login_failed_invalid_credentials',
	LOGIN_FAILED_USER_NOT_FOUND = 'login_failed_user_not_found',

	LOGOUT_SUCCESS = 'logout_success',
	LOGOUT_FAILED = 'logout_failed',

	REGISTER_SUCCESS = 'register_success',
	REGISTER_FAILED = 'register_failed',
	REGISTER_FAILED_USER_EXISTS = 'register_failed_user_exists',

	RESET_PASSWORD_SUCCESS = 'reset_password_success',
	RESET_PASSWORD_FAILED = 'reset_password_failed',
	RESET_PASSWORD_FAILED_USER_NOT_FOUND = 'reset_password_failed_user_not_found',
	RESET_PASSWORD_FAILED_INVALID_TOKEN = 'reset_password_failed_invalid_token',
	RESET_PASSWORD_FAILED_EXPIRED_TOKEN = 'reset_password_failed_expired_token',

	VERIFY_CONTACT_SUCCESS = 'verify_contact_success',
	VERIFY_CONTACT_FAILED = 'verify_contact_failed',
	VERIFY_CONTACT_FAILED_USER_NOT_FOUND = 'verify_contact_failed_user_not_found',
	VERIFY_CONTACT_FAILED_INVALID_TOKEN = 'verify_contact_failed_invalid_token',
	VERIFY_CONTACT_FAILED_EXPIRED_TOKEN = 'verify_contact_failed_expired_token',

}