export enum ResponseCode {
	// Success codes
	SUCCESS = 0,
  
	// General error codes
	INTERNAL_ERROR = 100,
	INVALID_INPUT = 101,
	VALIDATION_ERROR = 102,
	UNAUTHORIZED = 103,
	FORBIDDEN = 104,
	NOT_FOUND = 105,
	BAD_REQUEST = 106,
	CONFLICT = 107,
	TOO_MANY_REQUESTS = 108,
	UNPROCESSABLE_ENTITY = 109,
	SERVICE_UNAVAILABLE = 110,
  
	// User related error codes
	USER_NOT_FOUND = 200,
	USER_ALREADY_EXISTS = 201,
	USER_INACTIVE = 202,
	USER_PROFILE_NOT_FOUND = 203,
	USER_PROFILE_INCOMPLETE = 204,
	INVALID_USER_ID = 205,
	USER_EMAIL_NOT_VERIFIED = 206,
	USER_PHONE_NOT_VERIFIED = 207,
	USER_BLOCKED = 208,
	USER_DELETED = 209,
	USER_NOT_VERIFIED = 210,
	ACCOUNT_ALREADY_LINKED = 211,
	INVALID_USER_TYPE = 212,
	USER_NOT_LINKED = 213,
  
	// Auth related error codes
	INVALID_CREDENTIALS = 300,
	TOKEN_EXPIRED = 301,
	TOKEN_INVALID = 302,
	REFRESH_TOKEN_INVALID = 303,
	ACCESS_TOKEN_REQUIRED = 304,
	INSUFFICIENT_PERMISSIONS = 305,
	ACCOUNT_LOCKED = 306,
	PASSWORD_REQUIRED = 307,
	INVALID_PASSWORD_FORMAT = 308,
	PASSWORD_TOO_WEAK = 309,
	OLD_PASSWORD_INCORRECT = 310,
	LOGIN_ATTEMPTS_EXCEEDED = 311,
	EMAIL_NOT_VERIFIED = 312,
	PHONE_NOT_VERIFIED = 313,
	TOKEN_BLACKLISTED = 314,
	TOKEN_REVOKED = 315,
	INVALID_VERIFICATION_TOKEN = 316,
	CONTACT_CONFLICT = 317,
	CONTACT_REQUIRED = 318,
	INVALID_PASSWORD = 319,
	INVALID_SSO_TYPE = 320,
	INVALID_SSO_TOKEN = 321,
	SSO_TOKEN_VERIFY_FAILED = 322,
  
	// Payment related error codes
	PAYMENT_FAILED = 400,
	INSUFFICIENT_BALANCE = 401,
	INVALID_PAYMENT_METHOD = 402,
	PAYMENT_ALREADY_PROCESSED = 403,
	PAYMENT_CANCELLED = 404,
	PAYMENT_EXPIRED = 405,
	INVALID_AMOUNT = 406,
	MINIMUM_AMOUNT_NOT_MET = 407,
	MAXIMUM_AMOUNT_EXCEEDED = 408,
	PAYMENT_METHOD_NOT_SUPPORTED = 409,
  
	// VIP related error codes
	VIP_TIER_NOT_FOUND = 500,
	VIP_BENEFIT_NOT_FOUND = 501,
	INVALID_VIP_UPGRADE = 502,
	VIP_REQUIREMENT_NOT_MET = 503,
	VIP_TIER_ALREADY_EXISTS = 504,
	VIP_BENEFIT_ALREADY_EXISTS = 505,
  
	// File/Upload related error codes
	FILE_NOT_FOUND = 600,
	FILE_TOO_LARGE = 601,
	INVALID_FILE_FORMAT = 602,
	FILE_UPLOAD_FAILED = 603,
	FILE_PROCESSING_FAILED = 604,
  
	// Database related error codes
	DATABASE_CONNECTION_ERROR = 700,
	DUPLICATE_ENTRY = 701,
	FOREIGN_KEY_CONSTRAINT = 702,
	DATA_INTEGRITY_ERROR = 703,

	// Rate limiting error codes
	RATE_LIMIT_EXCEEDED = 800,
	DAILY_LIMIT_EXCEEDED = 801,
	MONTHLY_LIMIT_EXCEEDED = 802,
	// Two-factor authentication error codes
	TWO_FACTOR_REQUIRED = 803,
	INVALID_OTP_CODE = 804,
	OTP_ATTEMPTS_EXCEEDED = 805,
	OTP_EXPIRED = 806,
	
	// Zalo
	ZALO_OAUTH_FAILED = 900,
	ZALO_GET_EXCHANGE_TOKEN_FAILED = 901,
	ZALO_MISSING_REFRESH_TOKEN = 902,
	ZALO_MISSING_ACCESS_TOKEN = 903,
}

export const ResponseMessage: Record<ResponseCode, string> = {
	// Success messages
	[ResponseCode.SUCCESS]: 'Succcess',
  
	// General error messages
	[ResponseCode.INTERNAL_ERROR]: 'Internal server error occurred',
	[ResponseCode.INVALID_INPUT]: 'Invalid input provided',
	[ResponseCode.VALIDATION_ERROR]: 'Validation failed',
	[ResponseCode.UNAUTHORIZED]: 'Unauthorized access',
	[ResponseCode.FORBIDDEN]: 'Access forbidden',
	[ResponseCode.NOT_FOUND]: 'Resource not found',
	[ResponseCode.BAD_REQUEST]: 'Bad request',
	[ResponseCode.CONFLICT]: 'Resource conflict',
	[ResponseCode.TOO_MANY_REQUESTS]: 'Too many requests',
	[ResponseCode.UNPROCESSABLE_ENTITY]: 'Unprocessable entity',
	[ResponseCode.SERVICE_UNAVAILABLE]: 'Service temporarily unavailable',
  
	// User related error messages
	[ResponseCode.USER_NOT_FOUND]: 'User not found',
	[ResponseCode.USER_ALREADY_EXISTS]: 'User already exists',
	[ResponseCode.USER_INACTIVE]: 'User account is inactive',
	[ResponseCode.USER_PROFILE_NOT_FOUND]: 'User profile not found',
	[ResponseCode.USER_PROFILE_INCOMPLETE]: 'User profile is incomplete',
	[ResponseCode.INVALID_USER_ID]: 'Invalid user ID provided',
	[ResponseCode.USER_EMAIL_NOT_VERIFIED]: 'User email is not verified',
	[ResponseCode.USER_PHONE_NOT_VERIFIED]: 'User phone number is not verified',
	[ResponseCode.USER_BLOCKED]: 'User account has been blocked',
	[ResponseCode.USER_DELETED]: 'User account has been deleted',
	[ResponseCode.USER_NOT_VERIFIED]: 'User account is not verified',
	[ResponseCode.INVALID_USER_TYPE]: 'Invalid user type',
	[ResponseCode.ACCOUNT_ALREADY_LINKED]: 'Account is already linked to another user',
	[ResponseCode.USER_NOT_LINKED]: 'User is not linked to any social account',
  
	// Auth related error messages
	[ResponseCode.INVALID_CREDENTIALS]: 'Invalid email or password',
	[ResponseCode.TOKEN_EXPIRED]: 'Access token has expired',
	[ResponseCode.TOKEN_INVALID]: 'Invalid access token',
	[ResponseCode.REFRESH_TOKEN_INVALID]: 'Invalid refresh token',
	[ResponseCode.ACCESS_TOKEN_REQUIRED]: 'Access token is required',
	[ResponseCode.INSUFFICIENT_PERMISSIONS]: 'Insufficient permissions to perform this action',
	[ResponseCode.ACCOUNT_LOCKED]: 'Account has been temporarily locked',
	[ResponseCode.PASSWORD_REQUIRED]: 'Password has required',
	[ResponseCode.INVALID_PASSWORD_FORMAT]: 'Password format is invalid',
	[ResponseCode.PASSWORD_TOO_WEAK]: 'Password is too weak',
	[ResponseCode.OLD_PASSWORD_INCORRECT]: 'Current password is incorrect',
	[ResponseCode.LOGIN_ATTEMPTS_EXCEEDED]: 'Maximum login attempts exceeded',
	[ResponseCode.EMAIL_NOT_VERIFIED]: 'Email address is not verified',
	[ResponseCode.PHONE_NOT_VERIFIED]: 'Phone number is not verified',
	[ResponseCode.TOKEN_BLACKLISTED]: 'Token has been blacklisted',
	[ResponseCode.TOKEN_REVOKED]: 'Token has been revoked',
	[ResponseCode.INVALID_VERIFICATION_TOKEN]: 'Invalid verification token provided',
	[ResponseCode.CONTACT_CONFLICT]: 'Contact already exists',
	[ResponseCode.CONTACT_REQUIRED]: 'Contact is required',
	[ResponseCode.INVALID_PASSWORD]: 'Invalid password',
	[ResponseCode.INVALID_SSO_TYPE]: 'Invalid SSO type',
	[ResponseCode.INVALID_SSO_TOKEN]: 'Invalid SSO token',
	[ResponseCode.SSO_TOKEN_VERIFY_FAILED]: 'SSO token verify failed',

	// Payment related error messages
	[ResponseCode.PAYMENT_FAILED]: 'Payment processing failed',
	[ResponseCode.INSUFFICIENT_BALANCE]: 'Insufficient account balance',
	[ResponseCode.INVALID_PAYMENT_METHOD]: 'Invalid payment method',
	[ResponseCode.PAYMENT_ALREADY_PROCESSED]: 'Payment has already been processed',
	[ResponseCode.PAYMENT_CANCELLED]: 'Payment has been cancelled',
	[ResponseCode.PAYMENT_EXPIRED]: 'Payment has expired',
	[ResponseCode.INVALID_AMOUNT]: 'Invalid payment amount',
	[ResponseCode.MINIMUM_AMOUNT_NOT_MET]: 'Minimum payment amount not met',
	[ResponseCode.MAXIMUM_AMOUNT_EXCEEDED]: 'Maximum payment amount exceeded',
	[ResponseCode.PAYMENT_METHOD_NOT_SUPPORTED]: 'Payment method is not supported',
  
	// VIP related error messages
	[ResponseCode.VIP_TIER_NOT_FOUND]: 'VIP tier not found',
	[ResponseCode.VIP_BENEFIT_NOT_FOUND]: 'VIP benefit not found',
	[ResponseCode.INVALID_VIP_UPGRADE]: 'Invalid VIP tier upgrade',
	[ResponseCode.VIP_REQUIREMENT_NOT_MET]: 'VIP tier requirements not met',
	[ResponseCode.VIP_TIER_ALREADY_EXISTS]: 'VIP tier already exists',
	[ResponseCode.VIP_BENEFIT_ALREADY_EXISTS]: 'VIP benefit already exists',
  
	// File/Upload related error messages
	[ResponseCode.FILE_NOT_FOUND]: 'File not found',
	[ResponseCode.FILE_TOO_LARGE]: 'File size too large',
	[ResponseCode.INVALID_FILE_FORMAT]: 'Invalid file format',
	[ResponseCode.FILE_UPLOAD_FAILED]: 'File upload failed',
	[ResponseCode.FILE_PROCESSING_FAILED]: 'File processing failed',
  
	// Database related error messages
	[ResponseCode.DATABASE_CONNECTION_ERROR]: 'Database connection error',
	[ResponseCode.DUPLICATE_ENTRY]: 'Duplicate entry found',
	[ResponseCode.FOREIGN_KEY_CONSTRAINT]: 'Foreign key constraint violation',
	[ResponseCode.DATA_INTEGRITY_ERROR]: 'Data integrity constraint violation',
  
	// Rate limiting error messages
	[ResponseCode.RATE_LIMIT_EXCEEDED]: 'Rate limit exceeded',
	[ResponseCode.DAILY_LIMIT_EXCEEDED]: 'Daily limit exceeded',
	[ResponseCode.MONTHLY_LIMIT_EXCEEDED]: 'Monthly limit exceeded',
	
	// Two-factor authentication error messages
	[ResponseCode.TWO_FACTOR_REQUIRED]: 'Two-factor authentication is required',
	[ResponseCode.INVALID_OTP_CODE]: 'Invalid OTP code',
	[ResponseCode.OTP_ATTEMPTS_EXCEEDED]: 'Too many verification attempts. Please request a new OTP.',
	[ResponseCode.OTP_EXPIRED]:  'OTP has expired. Please request a new OTP.',
	
	// Zalo OAuth error messages
	[ResponseCode.ZALO_OAUTH_FAILED]: 'Zalo OAuth failed',
	[ResponseCode.ZALO_GET_EXCHANGE_TOKEN_FAILED]: 'Zalo get exchange token failed',
	[ResponseCode.ZALO_MISSING_REFRESH_TOKEN]:  'Zalo missing refresh token',
	[ResponseCode.ZALO_MISSING_ACCESS_TOKEN]: 'Zalo missing access token',
};