import type { MigrationInterface, QueryRunner } from 'typeorm';

export class InitTables21749973125057 implements MigrationInterface {
	name = 'InitTables21749973125057';

	public async up(queryRunner: QueryRunner): Promise<void> {
		// Create user profile table and indices
		await queryRunner.query(`
            CREATE TABLE "user_profile" (
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                "profile_id" BIGSERIAL NOT NULL,
                "user_id" bigint NOT NULL,
                "display_name" character varying(100),
                "gender" boolean,
                "dob" date,
                "avatar_url" character varying(500),
                "phone" character varying(20),
                "address" character varying(500),
                CONSTRAINT "UQ_eee360f3bff24af1b6890765201" UNIQUE ("user_id"),
                CONSTRAINT "PK_8c154faf15b98f494723d9cc45b" PRIMARY KEY ("profile_id")
            )
        `);
		await queryRunner.query(
			'CREATE UNIQUE INDEX "user_profile_user_id_key" ON "user_profile" ("user_id")',
		);
		await queryRunner.query(
			'CREATE UNIQUE INDEX "user_profile_pkey" ON "user_profile" ("profile_id")',
		);

		// Create user game mapping table and indices
		await queryRunner.query(`
            CREATE TABLE "user_game_mapping" (
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                "ugm_id" BIGSERIAL NOT NULL,
                "user_id" bigint NOT NULL,
                "game_id" integer NOT NULL,
                "game_balance" bigint NOT NULL DEFAULT 0,
                "assigned_at" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_d8feb9dadd4af9097b409c7eb27" PRIMARY KEY ("ugm_id")
            )
        `);
		await queryRunner.query(
			'CREATE UNIQUE INDEX "user_game_mapping_pkey" ON "user_game_mapping" ("ugm_id")',
		);
		await queryRunner.query(
			'CREATE INDEX "idx_user_game_mapping" ON "user_game_mapping" ("user_id", "game_id")',
		);

		// Create payment transaction table and indices
		await queryRunner.query(`
            CREATE TABLE "payment_transaction" (
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                "tx_id" BIGSERIAL NOT NULL,
                "user_id" bigint NOT NULL,
                "game_id" integer NOT NULL,
                "order_id" character varying(100) NOT NULL,
                "amount" numeric(10,2) NOT NULL,
                "currency" character varying(10) NOT NULL,
                "payment_method" character varying(30) NOT NULL,
                "status" character varying(30) NOT NULL DEFAULT 'pending',
                "note" character varying(100) NOT NULL,
                CONSTRAINT "UQ_91163b302301738c73b0b917a1c" UNIQUE ("order_id"),
                CONSTRAINT "PK_43d7c92466f8b34e443d9bc0a03" PRIMARY KEY ("tx_id")
            )
        `);
		await queryRunner.query(
			'CREATE INDEX "idx_payment_user" ON "payment_transaction" ("user_id")',
		);
		await queryRunner.query(
			'CREATE UNIQUE INDEX "payment_transaction_pkey" ON "payment_transaction" ("tx_id")',
		);
		await queryRunner.query(
			'CREATE INDEX "idx_payment_status" ON "payment_transaction" ("status")',
		);
		await queryRunner.query(
			'CREATE UNIQUE INDEX "payment_transaction_order_id_key" ON "payment_transaction" ("order_id")',
		);
		await queryRunner.query(
			'CREATE INDEX "idx_payment_game" ON "payment_transaction" ("game_id")',
		);

		// Create admin role mapping table and indices
		await queryRunner.query(`
            CREATE TABLE "admin_role_mapping" (
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                "arm_id" BIGSERIAL NOT NULL,
                "admin_id" bigint NOT NULL,
                "role_id" smallint NOT NULL,
                "game_id" integer,
                "assigned_at" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_cc9b91cc2bda36aa9740a1fc69a" PRIMARY KEY ("arm_id")
            )
        `);
		await queryRunner.query(
			'CREATE UNIQUE INDEX "admin_role_mapping_pkey" ON "admin_role_mapping" ("arm_id")',
		);
		await queryRunner.query(
			'CREATE INDEX "idx_admin_role_mapping" ON "admin_role_mapping" ("admin_id", "game_id", "role_id")',
		);

		// Create role table and indices
		await queryRunner.query(`
            CREATE TABLE "role" (
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                "role_id" SMALLSERIAL NOT NULL,
                "role_name" character varying(30) NOT NULL,
                "description" character varying(255),
                CONSTRAINT "UQ_4810bc474fe6394c6f58cb7c9e5" UNIQUE ("role_name"),
                CONSTRAINT "PK_df46160e6aa79943b83c81e496e" PRIMARY KEY ("role_id")
            )
        `);
		await queryRunner.query(
			'CREATE UNIQUE INDEX "role_role_name_key" ON "role" ("role_name")',
		);
		await queryRunner.query(
			'CREATE UNIQUE INDEX "role_pkey" ON "role" ("role_id")',
		);

		// Create admin account table and indices
		await queryRunner.query(`
            CREATE TABLE "admin_account" (
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                "admin_id" BIGSERIAL NOT NULL,
                "email" character varying(255),
                "password_hash" character varying(255) NOT NULL,
                "status" character varying(30) NOT NULL DEFAULT 'active',
                "last_login_at" TIMESTAMP NOT NULL,
                CONSTRAINT "UQ_f3e0154c250dadde6d843ffc220" UNIQUE ("email"),
                CONSTRAINT "PK_bd4ab6315995cbb3e9c1274a589" PRIMARY KEY ("admin_id")
            )
        `);
		await queryRunner.query(
			'CREATE UNIQUE INDEX "admin_account_email_key" ON "admin_account" ("email")',
		);
		await queryRunner.query(
			'CREATE UNIQUE INDEX "admin_account_pkey" ON "admin_account" ("admin_id")',
		);

		// Create audit log table and indices
		await queryRunner.query(`
            CREATE TABLE "audit_log" (
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                "log_id" BIGSERIAL NOT NULL,
                "user_id" bigint,
                "admin_id" bigint,
                "action_type" character varying(50) NOT NULL,
                "description" character varying(500),
                "ip_address" character varying(45),
                CONSTRAINT "PK_80cbc678974d588828aeec45514" PRIMARY KEY ("log_id")
            )
        `);
		await queryRunner.query(
			'CREATE INDEX "idx_audit_user" ON "audit_log" ("user_id")',
		);
		await queryRunner.query(
			'CREATE UNIQUE INDEX "audit_log_pkey" ON "audit_log" ("log_id")',
		);
		await queryRunner.query(
			'CREATE INDEX "idx_audit_action" ON "audit_log" ("action_type")',
		);

		// Create user account table and indices
		await queryRunner.query(`
            CREATE TABLE "user_account" (
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                "user_id" BIGSERIAL NOT NULL,
                "username" character varying(100) NOT NULL,
                "email" character varying(255),
                "password_hash" character varying(255),
                "refresh_token" character varying(1000),
                "refresh_token_expires_at" TIMESTAMP,
                "social_access_token" character varying(1000),
                "social_refresh_token" character varying(1000),
                "status" character varying(30) NOT NULL DEFAULT 'active',
                "account_type" character varying(30) NOT NULL DEFAULT 'quickplay',
                "social_uid" character varying(255),
                "linked_at" TIMESTAMP,
                "user_balance" bigint NOT NULL DEFAULT 0,
                "last_login_at" TIMESTAMP,
                "created_at_ip" character varying(45),
                "last_login_at_ip" character varying(45),
                CONSTRAINT "UQ_56a0e4bcec2b5411beafa47ffxx" UNIQUE ("username"),
                CONSTRAINT "UQ_56a0e4bcec2b5411beafa47ffa5" UNIQUE ("email"),
                CONSTRAINT "PK_1e7af5387f4169347ddef6e8180" PRIMARY KEY ("user_id")
            )
        `);
		await queryRunner.query(
			'CREATE UNIQUE INDEX "user_account_pkey" ON "user_account" ("user_id")',
		);
		await queryRunner.query(
			'CREATE UNIQUE INDEX "user_account_username_key" ON "user_account" ("username")',
		);
		await queryRunner.query(
			'CREATE UNIQUE INDEX "user_account_email_key" ON "user_account" ("email")',
		);

		// Create game table and indices
		await queryRunner.query(`
            CREATE TABLE "game" (
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                "game_id" SERIAL NOT NULL,
                "game_key" character varying(50) NOT NULL,
                "game_name" character varying(100) NOT NULL,
                CONSTRAINT "UQ_470bd08c8f51ed21a5f09d80fc2" UNIQUE ("game_key"),
                CONSTRAINT "PK_5b09eea8ea244730f3f339e3152" PRIMARY KEY ("game_id")
            )
        `);
		await queryRunner.query(
			'CREATE UNIQUE INDEX "game_game_key_key" ON "game" ("game_key")',
		);
		await queryRunner.query(
			'CREATE UNIQUE INDEX "game_pkey" ON "game" ("game_id")',
		);

		// Add foreign key constraints
		await queryRunner.query(
			'ALTER TABLE "user_profile" ADD CONSTRAINT "FK_eee360f3bff24af1b6890765201" FOREIGN KEY ("user_id") REFERENCES "user_account"("user_id") ON DELETE CASCADE ON UPDATE NO ACTION',
		);
		await queryRunner.query(
			'ALTER TABLE "user_game_mapping" ADD CONSTRAINT "FK_0a847860a83fe94ed083c757b23" FOREIGN KEY ("user_id") REFERENCES "user_account"("user_id") ON DELETE NO ACTION ON UPDATE NO ACTION',
		);
		await queryRunner.query(
			'ALTER TABLE "user_game_mapping" ADD CONSTRAINT "FK_e4bfb691f7d5a304321fb12d80b" FOREIGN KEY ("game_id") REFERENCES "game"("game_id") ON DELETE CASCADE ON UPDATE NO ACTION',
		);
		await queryRunner.query(
			'ALTER TABLE "payment_transaction" ADD CONSTRAINT "FK_6f438a44a054f881f3e8813174f" FOREIGN KEY ("game_id") REFERENCES "game"("game_id") ON DELETE NO ACTION ON UPDATE NO ACTION',
		);
		await queryRunner.query(
			'ALTER TABLE "payment_transaction" ADD CONSTRAINT "FK_0362c4a86732d07164a772f8292" FOREIGN KEY ("user_id") REFERENCES "user_account"("user_id") ON DELETE NO ACTION ON UPDATE NO ACTION',
		);
		await queryRunner.query(
			'ALTER TABLE "admin_role_mapping" ADD CONSTRAINT "FK_4ab01c6f3fec1948cef772677d8" FOREIGN KEY ("admin_id") REFERENCES "admin_account"("admin_id") ON DELETE NO ACTION ON UPDATE NO ACTION',
		);
		await queryRunner.query(
			'ALTER TABLE "admin_role_mapping" ADD CONSTRAINT "FK_18a2119ff41cf21da688eef4d13" FOREIGN KEY ("game_id") REFERENCES "game"("game_id") ON DELETE CASCADE ON UPDATE NO ACTION',
		);
		await queryRunner.query(
			'ALTER TABLE "admin_role_mapping" ADD CONSTRAINT "FK_bccf13ef950f398b26e298a29cb" FOREIGN KEY ("role_id") REFERENCES "role"("role_id") ON DELETE NO ACTION ON UPDATE NO ACTION',
		);
		await queryRunner.query(
			'ALTER TABLE "audit_log" ADD CONSTRAINT "FK_0de5650de773ff3e481357151bf" FOREIGN KEY ("admin_id") REFERENCES "admin_account"("admin_id") ON DELETE NO ACTION ON UPDATE NO ACTION',
		);
		await queryRunner.query(
			'ALTER TABLE "audit_log" ADD CONSTRAINT "FK_cb11bd5b662431ea0ac455a27d7" FOREIGN KEY ("user_id") REFERENCES "user_account"("user_id") ON DELETE NO ACTION ON UPDATE NO ACTION',
		);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		// Drop foreign key constraints first
		await queryRunner.query(
			'ALTER TABLE "audit_log" DROP CONSTRAINT "FK_cb11bd5b662431ea0ac455a27d7"',
		);
		await queryRunner.query(
			'ALTER TABLE "audit_log" DROP CONSTRAINT "FK_0de5650de773ff3e481357151bf"',
		);
		await queryRunner.query(
			'ALTER TABLE "admin_role_mapping" DROP CONSTRAINT "FK_bccf13ef950f398b26e298a29cb"',
		);
		await queryRunner.query(
			'ALTER TABLE "admin_role_mapping" DROP CONSTRAINT "FK_18a2119ff41cf21da688eef4d13"',
		);
		await queryRunner.query(
			'ALTER TABLE "admin_role_mapping" DROP CONSTRAINT "FK_4ab01c6f3fec1948cef772677d8"',
		);
		await queryRunner.query(
			'ALTER TABLE "payment_transaction" DROP CONSTRAINT "FK_0362c4a86732d07164a772f8292"',
		);
		await queryRunner.query(
			'ALTER TABLE "payment_transaction" DROP CONSTRAINT "FK_6f438a44a054f881f3e8813174f"',
		);
		await queryRunner.query(
			'ALTER TABLE "user_game_mapping" DROP CONSTRAINT "FK_e4bfb691f7d5a304321fb12d80b"',
		);
		await queryRunner.query(
			'ALTER TABLE "user_game_mapping" DROP CONSTRAINT "FK_0a847860a83fe94ed083c757b23"',
		);
		await queryRunner.query(
			'ALTER TABLE "user_profile" DROP CONSTRAINT "FK_eee360f3bff24af1b6890765201"',
		);

		// Drop tables and their indices in reverse order of creation
		// Drop user profile related
		await queryRunner.query('DROP INDEX "public"."user_profile_pkey"');
		await queryRunner.query('DROP INDEX "public"."user_profile_user_id_key"');
		await queryRunner.query('DROP TABLE "user_profile"');

		// Drop user game mapping related
		await queryRunner.query('DROP INDEX "public"."idx_user_game_mapping"');
		await queryRunner.query('DROP INDEX "public"."user_game_mapping_pkey"');
		await queryRunner.query('DROP TABLE "user_game_mapping"');

		// Drop payment transaction related
		await queryRunner.query('DROP INDEX "public"."idx_payment_game"');
		await queryRunner.query(
			'DROP INDEX "public"."payment_transaction_order_id_key"',
		);
		await queryRunner.query('DROP INDEX "public"."idx_payment_status"');
		await queryRunner.query('DROP INDEX "public"."payment_transaction_pkey"');
		await queryRunner.query('DROP INDEX "public"."idx_payment_user"');
		await queryRunner.query('DROP TABLE "payment_transaction"');

		// Drop admin role mapping related
		await queryRunner.query('DROP INDEX "public"."idx_admin_role_mapping"');
		await queryRunner.query('DROP INDEX "public"."admin_role_mapping_pkey"');
		await queryRunner.query('DROP TABLE "admin_role_mapping"');

		// Drop role related
		await queryRunner.query('DROP INDEX "public"."role_pkey"');
		await queryRunner.query('DROP INDEX "public"."role_role_name_key"');
		await queryRunner.query('DROP TABLE "role"');

		// Drop admin account related
		await queryRunner.query('DROP INDEX "public"."admin_account_pkey"');
		await queryRunner.query('DROP INDEX "public"."admin_account_email_key"');
		await queryRunner.query('DROP TABLE "admin_account"');

		// Drop audit log related
		await queryRunner.query('DROP INDEX "public"."idx_audit_action"');
		await queryRunner.query('DROP INDEX "public"."audit_log_pkey"');
		await queryRunner.query('DROP INDEX "public"."idx_audit_user"');
		await queryRunner.query('DROP TABLE "audit_log"');

		// Drop user account related
		await queryRunner.query('DROP INDEX "public"."user_account_email_key"');
		await queryRunner.query('DROP INDEX "public"."user_account_pkey"');
		await queryRunner.query('DROP TABLE "user_account"');

		// Drop game related
		await queryRunner.query('DROP INDEX "public"."game_pkey"');
		await queryRunner.query('DROP INDEX "public"."game_game_key_key"');
		await queryRunner.query('DROP TABLE "game"');
	}
}
