import type { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPhoneToUserAccount1755096467507 implements MigrationInterface {
	name = 'AddPhoneToUserAccount1755096467507';

	public async up(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(
			'ALTER TABLE "user_account" ADD "phone" character varying(15)',
		);
		await queryRunner.query(
			'ALTER TABLE "user_account" ADD CONSTRAINT "UQ_user_account_phone" UNIQUE ("phone")',
		);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(
			'ALTER TABLE "user_account" DROP CONSTRAINT "UQ_user_account_phone"',
		);
		await queryRunner.query('ALTER TABLE "user_account" DROP COLUMN "phone"');
	}
}
