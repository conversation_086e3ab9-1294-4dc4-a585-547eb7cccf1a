import type { MigrationInterface, QueryRunner } from 'typeorm';

export class ImplementUserVerificationSystem1755236351471 implements MigrationInterface {
	name = 'ImplementUserVerificationSystem1755236351471';

	public async up(queryRunner: QueryRunner): Promise<void> {
		// 1. Add verification fields to user_account table
		await queryRunner.query(`
			ALTER TABLE "user_account"
			ADD COLUMN "is_email_verified" boolean NOT NULL DEFAULT false
		`);

		await queryRunner.query(`
			ALTER TABLE "user_account"
			ADD COLUMN "is_phone_verified" boolean NOT NULL DEFAULT false
		`);

		// 2. Add identifier_number field to user_profile table
		await queryRunner.query(`
			ALTER TABLE "user_profile"
			ADD COLUMN "identifier_number" character varying(15) NULL
		`);

		// 3. Remove phone field from user_profile table (phone is now in user_account)
		await queryRunner.query(`
			ALTER TABLE "user_profile"
			DROP COLUMN IF EXISTS "phone"
		`);

		// 4. Change gender from boolean to enum
		// Create gender enum type
		await queryRunner.query(`
			CREATE TYPE "gender_enum" AS ENUM ('female', 'male', 'other')
		`);

		// Add temporary column with enum type
		await queryRunner.query(`
			ALTER TABLE "user_profile"
			ADD COLUMN "gender_new" "gender_enum" NULL
		`);

		// Migrate existing data from boolean to enum
		// true (male) -> 'male', false (female) -> 'female', null -> null
		await queryRunner.query(`
			UPDATE "user_profile"
			SET "gender_new" = CASE
				WHEN "gender" = true THEN 'male'::gender_enum
				WHEN "gender" = false THEN 'female'::gender_enum
				ELSE NULL
			END
		`);

		// Drop old boolean column
		await queryRunner.query(`
			ALTER TABLE "user_profile" DROP COLUMN "gender"
		`);

		// Rename new column to original name
		await queryRunner.query(`
			ALTER TABLE "user_profile"
			RENAME COLUMN "gender_new" TO "gender"
		`);

		// 5. Create indexes for better performance
		await queryRunner.query(`
			CREATE INDEX "IDX_user_account_is_email_verified" ON "user_account" ("is_email_verified")
		`);

		await queryRunner.query(`
			CREATE INDEX "IDX_user_account_is_phone_verified" ON "user_account" ("is_phone_verified")
		`);

		await queryRunner.query(`
			CREATE INDEX "IDX_user_profile_identifier_number" ON "user_profile" ("identifier_number")
		`);

		await queryRunner.query(`
			CREATE INDEX "IDX_user_profile_gender" ON "user_profile" ("gender")
		`);

		// Drop old phone index if exists
		await queryRunner.query(`
			DROP INDEX IF EXISTS "IDX_user_profile_phone"
		`);

		// 6. Set verification status for existing users based on their registration method
		// Users with email should have email verified
		await queryRunner.query(`
			UPDATE "user_account"
			SET "is_email_verified" = true
			WHERE "email" IS NOT NULL AND "email" != ''
		`);

		// Users with phone should have phone verified
		await queryRunner.query(`
			UPDATE "user_account"
			SET "is_phone_verified" = true
			WHERE "phone" IS NOT NULL AND "phone" != ''
		`);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		// Revert in reverse order

		// 1. Add phone field back to user_profile table
		await queryRunner.query(`
			ALTER TABLE "user_profile"
			ADD COLUMN "phone" character varying(15) NULL
		`);

		// 2. Create phone index
		await queryRunner.query(`
			CREATE INDEX "IDX_user_profile_phone" ON "user_profile" ("phone")
		`);

		// 3. Revert gender from enum back to boolean
		// Drop gender index
		await queryRunner.query(`
			DROP INDEX IF EXISTS "IDX_user_profile_gender"
		`);

		// Add temporary boolean column
		await queryRunner.query(`
			ALTER TABLE "user_profile"
			ADD COLUMN "gender_old" boolean NULL
		`);

		// Migrate data back from enum to boolean
		// 'male' -> true, 'female' -> false, 'other' -> null, null -> null
		await queryRunner.query(`
			UPDATE "user_profile"
			SET "gender_old" = CASE
				WHEN "gender" = 'male' THEN true
				WHEN "gender" = 'female' THEN false
				ELSE NULL
			END
		`);

		// Drop enum column
		await queryRunner.query(`
			ALTER TABLE "user_profile" DROP COLUMN "gender"
		`);

		// Rename boolean column back
		await queryRunner.query(`
			ALTER TABLE "user_profile"
			RENAME COLUMN "gender_old" TO "gender"
		`);

		// Drop enum type
		await queryRunner.query(`
			DROP TYPE "gender_enum"
		`);

		// 4. Drop other indexes
		await queryRunner.query(`
			DROP INDEX IF EXISTS "IDX_user_profile_identifier_number"
		`);

		await queryRunner.query(`
			DROP INDEX IF EXISTS "IDX_user_account_is_phone_verified"
		`);

		await queryRunner.query(`
			DROP INDEX IF EXISTS "IDX_user_account_is_email_verified"
		`);

		// 5. Drop columns
		await queryRunner.query(`
			ALTER TABLE "user_profile" DROP COLUMN "identifier_number"
		`);

		await queryRunner.query(`
			ALTER TABLE "user_account" DROP COLUMN "is_phone_verified"
		`);

		await queryRunner.query(`
			ALTER TABLE "user_account" DROP COLUMN "is_email_verified"
		`);
	}
}
