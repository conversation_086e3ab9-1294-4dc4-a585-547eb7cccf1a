// src/databases/migrations/1755236351472-create-audit-log-table.ts
import type { MigrationInterface, QueryRunner } from 'typeorm';
import { Table, TableIndex } from 'typeorm';

export class CreateAuditLogTable1755236351472 implements MigrationInterface {
	public async up(queryRunner: QueryRunner): Promise<void> {
		// Create enum types first		
		await queryRunner.query(`
			CREATE TYPE "audit_log_level_enum" AS ENUM (
				'info',
				'warning',
				'error',
				'critical'
			)
		`);

		await queryRunner.query('DROP TABLE IF EXISTS "audit_log"');
		await queryRunner.createTable(
			new Table({
				name: 'audit_log',
				columns: [
					{
						name: 'id',
						type: 'bigint',
						isPrimary: true,
						isGenerated: true,
						generationStrategy: 'increment',
					},
					{
						name: 'user_id',
						type: 'bigint',
						isNullable: true,
					},
					{
						name: 'action',
						type: 'varchar',
						length: '255',
					},
					{
						name: 'category',
						type: 'varchar',
						default: '\'system\'',
					},
					{
						name: 'level',
						type: 'audit_log_level_enum',
						default: '\'info\'',
					},
					{
						name: 'context',
						type: 'jsonb',
						isNullable: true,
					},
					{
						name: 'metadata',
						type: 'jsonb',
						isNullable: true,
					},
					{
						name: 'ip',
						type: 'varchar',
						length: '45',
						isNullable: true,
					},
					{
						name: 'user_agent',
						type: 'text',
						isNullable: true,
					},
					{
						name: 'session_id',
						type: 'varchar',
						length: '255',
						isNullable: true,
					},
					{
						name: 'request_id',
						type: 'varchar',
						length: '255',
						isNullable: true,
					},
					{
						name: 'success',
						type: 'boolean',
						default: true,
					},
					{
						name: 'error_message',
						type: 'text',
						isNullable: true,
					},
					{
						name: 'created_at',
						type: 'timestamp',
						default: 'now()',
					},
				],
			}),
			true,
		);

		// Create indexes
		await queryRunner.createIndex('audit_log', new TableIndex({ name: 'IDX_AUDIT_LOG_USER_ACTION', columnNames: ['user_id', 'action'] }));
		await queryRunner.createIndex('audit_log', new TableIndex({ name: 'IDX_AUDIT_LOG_CATEGORY_CREATED', columnNames: ['category', 'created_at'] }));
		await queryRunner.createIndex('audit_log', new TableIndex({ name: 'IDX_AUDIT_LOG_LEVEL_CREATED', columnNames: ['level', 'created_at'] }));
		await queryRunner.createIndex('audit_log', new TableIndex({ name: 'IDX_AUDIT_LOG_CREATED', columnNames: ['created_at'] }));
		await queryRunner.createIndex('audit_log', new TableIndex({ name: 'IDX_AUDIT_LOG_SUCCESS', columnNames: ['success'] }));
		// Add new composite indexes
		await queryRunner.createIndex('audit_log', new TableIndex({
			name: 'IDX_AUDIT_LOG_USER_CATEGORY_CREATED',
			columnNames: ['user_id', 'category', 'created_at'],
		}),
		);
		
		await queryRunner.createIndex('audit_log', new TableIndex({
			name: 'IDX_AUDIT_LOG_USER_SUCCESS_CREATED',
			columnNames: ['user_id', 'success', 'created_at'],
		}),
		);
		
		await queryRunner.createIndex('audit_log', new TableIndex({
			name: 'IDX_AUDIT_LOG_CATEGORY_LEVEL_CREATED',
			columnNames: ['category', 'level', 'created_at'],
		}),
		);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.dropTable('audit_logs', true);
		await queryRunner.dropTable('audit_log', true);
		
		// Drop enum types
		await queryRunner.query('DROP TYPE IF EXISTS "audit_log_level_enum"');
	}
}