import { type MigrationInterface, type QueryRunner, TableColumn } from 'typeorm';

export class AddIdRelatedFieldsToUserProfileTables1755236351472 implements MigrationInterface {
	public async up(queryRunner: QueryRunner): Promise<void> {
		// Add columns to user_profile table
		await queryRunner.addColumns('user_profile', [
			new TableColumn({
				name: 'id_issue_date',
				type: 'date',
				isNullable: true,
			}),
			new TableColumn({
				name: 'id_front_url',
				type: 'varchar',
				length: '500',
				isNullable: true,
			}),
			new TableColumn({
				name: 'id_back_url',
				type: 'varchar',
				length: '500',
				isNullable: true,
			}),
		]);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		// Remove columns from user_profile
		await queryRunner.dropColumns('user_profile', [
			'id_issue_date',
			'id_front_url',
			'id_back_url',
		]);
	}
}