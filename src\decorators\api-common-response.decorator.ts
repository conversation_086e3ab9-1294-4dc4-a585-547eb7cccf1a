import { ResponseDto } from '@common/dto/response.dto';
import { ResponseCode } from '@constants/response-codes';
import type { Type } from '@nestjs/common';
import { applyDecorators } from '@nestjs/common';
import {
	ApiExtraModels,
	ApiResponse,
	type ApiResponseExamples,
	getSchemaPath,
} from '@nestjs/swagger';
import type {
	ReferenceObject,
	SchemaObject,
} from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';

/**
 * Common response decorator that:
 *  • wraps any returned DTO into `ResponseDto<Dto>`
 *  • adds `statusCode`, `message` to the examples
 */
export function ApiCommonResponse<T extends Type>(options: {
	type?: T | [T];
	status?: number;
	description?: string;
	examples?: Record<string, ApiResponseExamples>;
}): MethodDecorator {
	const { type, status, description, examples } = options;

	/* --------- DTO & schema ---------- */
	const isArray = Array.isArray(type);
	const dto = isArray ? type![0] : type;

	const dataSchema: SchemaObject | ReferenceObject = dto
		? isArray
			? {
				type: 'array',
				items: { $ref: getSchemaPath(dto) },
			}
			: { $ref: getSchemaPath(dto) }
		: { type: 'object', nullable: true };

	/* ---------- extra models ---------- */
	const extraModels = dto ? [ResponseDto, dto] : [ResponseDto];

	/* --------- examples (if any) --------- */
	const wrappedExamples: Record<string, ApiResponseExamples> | undefined =
    examples
    	? Object.entries(examples).reduce<Record<string, ApiResponseExamples>>(
    		(acc, [key, example]) => {
    			acc[key] = {
    				...example,
    				value: {
    					statusCode: ResponseCode.SUCCESS,
    					message: 'OK',
    					data: example.value,
    				},
    			};
    			return acc;
    		},
    		{},
    	)
    	: undefined;

	/* --------- apply decorators --------- */
	return applyDecorators(
		...extraModels.map((model) => ApiExtraModels(model)),
		ApiResponse({
			status: status ?? 200,
			description,
			schema: {
				allOf: [
					{ $ref: getSchemaPath(ResponseDto) },
					{
						properties: {
							data: dataSchema,
						},
					},
				],
			},
			examples: wrappedExamples,
		}),
	);
}
