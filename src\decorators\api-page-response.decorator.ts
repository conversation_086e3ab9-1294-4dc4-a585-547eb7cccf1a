import { PageDto } from '@common/dto/page.dto';
import { ResponseDto } from '@common/dto/response.dto';
import type { Type } from '@nestjs/common';
import { applyDecorators } from '@nestjs/common';
import { ApiExtraModels, ApiOkResponse, getSchemaPath } from '@nestjs/swagger';

export function ApiPageResponse<T extends Type>(options: {
	type: T;
	description?: string;
}): MethodDecorator {
	return applyDecorators(
		ApiExtraModels(ResponseDto, PageDto, options.type),
		ApiOkResponse({
			description: options.description,
			schema: {
				allOf: [
					{ $ref: getSchemaPath(ResponseDto) },
					{
						properties: {
							data: {
								allOf: [
									{ $ref: getSchemaPath(PageDto) },
									{
										properties: {
											results: {
												type: 'array',
												items: { $ref: getSchemaPath(options.type) },
											},
										},
									},
								],
							},
						},
					},
				],
			},
		}),
	);
}