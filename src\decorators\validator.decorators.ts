import type { ValidationOptions } from 'class-validator';
import {
	IsPhoneNumber as isPhone<PERSON><PERSON>ber,
	registerDecorator,
	ValidateIf,
} from 'class-validator';
import _ from 'lodash';

export function IsPassword(
	validationOptions?: ValidationOptions,
): PropertyDecorator {
	return (object, propertyName) => {
		registerDecorator({
			propertyName: propertyName as string,
			name: 'isPassword',
			target: object.constructor,
			constraints: [],
			options: validationOptions,
			validator: {
				validate(value: string) {
					return /^[\d!#$%&*@A-Z^a-z]*$/.test(value);
				},
			},
		});
	};
}

export function IsPhoneNumber(
	validationOptions?: ValidationOptions & {
		region?: Parameters<typeof isPhoneNumber>[0];
	},
): PropertyDecorator {
	return (object, propertyName) => {
		registerDecorator({
			propertyName: propertyName as string,
			name: 'isVietnamesePhoneNumber',
			target: object.constructor,
			constraints: [],
			options: {
				message:
					'Phone number must be in Vietnamese format: 84 followed by 9-10 digits (e.g., 84905060708)',
				...validationOptions,
			},
			validator: {
				validate(value: string) {
					// Vietnamese phone number format: 84 + exactly 9 or 10 digits
					return /^84[0-9]{9}$|^84[0-9]{10}$/.test(value);
				},
			},
		});
	};
}

export function IsTmpKey(
	validationOptions?: ValidationOptions,
): PropertyDecorator {
	return (object, propertyName) => {
		registerDecorator({
			propertyName: propertyName as string,
			name: 'tmpKey',
			target: object.constructor,
			options: validationOptions,
			validator: {
				validate(value: string): boolean {
					return _.isString(value) && value.startsWith('tmp/');
				},
				defaultMessage(): string {
					return 'error.invalidTmpKey';
				},
			},
		});
	};
}

export function IsUndefinable(options?: ValidationOptions): PropertyDecorator {
	return ValidateIf((_obj, value) => value !== undefined, options);
}

export function IsNullable(options?: ValidationOptions): PropertyDecorator {
	return ValidateIf((_obj, value) => value !== null, options);
}
