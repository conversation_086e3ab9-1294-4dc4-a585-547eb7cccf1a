import { generateHash } from '@common/utils';
import { UserAccountEntity } from '@modules/user/user-account.entity';
import type {
	EntitySubscriberInterface,
	InsertEvent,
	UpdateEvent,
} from 'typeorm';
import { EventSubscriber } from 'typeorm';

@EventSubscriber()
export class UserSubscriber
implements EntitySubscriberInterface<UserAccountEntity> {
	listenTo(): typeof UserAccountEntity {
		return UserAccountEntity;
	}

	beforeInsert(event: InsertEvent<UserAccountEntity>): void {
		if (event.entity.passwordHash) {
			event.entity.passwordHash = generateHash(event.entity.passwordHash);
		}
	}

	beforeUpdate(event: UpdateEvent<UserAccountEntity>): void {
		// Handle both save() and update() calls
		const entity = event.entity as UserAccountEntity;

		// For update() calls, entity might be undefined, check event.queryRunner
		if (!entity) {
			return; // Skip if no entity (bulk update)
		}

		if (
			entity.passwordHash &&
			event.databaseEntity &&
			entity.passwordHash !== event.databaseEntity.passwordHash
		) {
			entity.passwordHash = generateHash(entity.passwordHash);
		}
	}
}
