import { ResponseCode, ResponseMessage } from '@constants/response-codes';
import { HttpException, HttpStatus } from '@nestjs/common';

export class AppException extends HttpException {
	public readonly errorCode: ResponseCode;

	constructor(
		errorCode: ResponseCode,
		message?: string,
		status: HttpStatus = HttpStatus.INTERNAL_SERVER_ERROR,
	) {
		const finalMessage = message || ResponseMessage[errorCode];
		super(finalMessage, status);
		this.errorCode = errorCode;
	}

	getErrorCode(): ResponseCode {
		return this.errorCode;
	}
}