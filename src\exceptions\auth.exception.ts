import { ResponseCode, ResponseMessage } from '@constants/response-codes';
import { HttpStatus } from '@nestjs/common';

import { AppException } from './app.exception';

//Đang đợi được xài
export class InvalidCredentialsException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.INVALID_CREDENTIALS,
			message || ResponseMessage[ResponseCode.INVALID_CREDENTIALS],
			HttpStatus.UNAUTHORIZED,
		);
	}
}

export class InvalidPasswordException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.INVALID_PASSWORD,
			message || ResponseMessage[ResponseCode.INVALID_PASSWORD],
			HttpStatus.UNAUTHORIZED,
		);
	}
}

export class AccountNotActiveException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.USER_INACTIVE,
			message || ResponseMessage[ResponseCode.USER_INACTIVE],
			HttpStatus.FORBIDDEN,
		);
	}
}

export class InvalidRefreshTokenException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.REFRESH_TOKEN_INVALID,
			message || ResponseMessage[ResponseCode.REFRESH_TOKEN_INVALID],
			HttpStatus.UNAUTHORIZED,
		);
	}
}

export class TokenRevokedException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.TOKEN_REVOKED,
			message || ResponseMessage[ResponseCode.TOKEN_REVOKED],
			HttpStatus.UNAUTHORIZED,
		);
	}
}

export class AccountAlreadyExistsException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.USER_ALREADY_EXISTS,
			message || ResponseMessage[ResponseCode.USER_ALREADY_EXISTS],
			HttpStatus.CONFLICT,
		);
	}
}

export class QuickplayLinkException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.FORBIDDEN,
			message || ResponseMessage[ResponseCode.FORBIDDEN],
			HttpStatus.FORBIDDEN,
		);
	}
}

export class TooManyLoginAttemptsException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.LOGIN_ATTEMPTS_EXCEEDED,
			message || ResponseMessage[ResponseCode.LOGIN_ATTEMPTS_EXCEEDED],
			HttpStatus.TOO_MANY_REQUESTS,
		);
	}
}

export class IncorrectPasswordException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.OLD_PASSWORD_INCORRECT,
			message || ResponseMessage[ResponseCode.OLD_PASSWORD_INCORRECT],
			HttpStatus.BAD_REQUEST,
		);
	}
}

export class TokenBlacklistedException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.TOKEN_BLACKLISTED,
			message || ResponseMessage[ResponseCode.TOKEN_BLACKLISTED],
			HttpStatus.BAD_REQUEST,
		);
	}
}

export class InvalidVerificationTokenException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.INVALID_VERIFICATION_TOKEN,
			message || ResponseMessage[ResponseCode.INVALID_VERIFICATION_TOKEN],
			HttpStatus.BAD_REQUEST,
		);
	}
}

export class TokenExpiredException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.TOKEN_EXPIRED,
			message || ResponseMessage[ResponseCode.TOKEN_EXPIRED],
			HttpStatus.UNAUTHORIZED,
		);
	}
}

export class TokenInvalidException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.TOKEN_INVALID,
			message || ResponseMessage[ResponseCode.TOKEN_INVALID],
			HttpStatus.UNAUTHORIZED,
		);
	}
}

//Đang đợi được xài
export class AccessTokenRequiredException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.ACCESS_TOKEN_REQUIRED,
			message || ResponseMessage[ResponseCode.ACCESS_TOKEN_REQUIRED],
			HttpStatus.UNAUTHORIZED,
		);
	}
}

//Đang đợi được xài
export class InsufficientPermissionsException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.INSUFFICIENT_PERMISSIONS,
			message || ResponseMessage[ResponseCode.INSUFFICIENT_PERMISSIONS],
			HttpStatus.FORBIDDEN,
		);
	}
}

//Đang đợi được xài
export class AccountLockedException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.ACCOUNT_LOCKED,
			message || ResponseMessage[ResponseCode.ACCOUNT_LOCKED],
			HttpStatus.FORBIDDEN,
		);
	}
}

export class PasswordRequiredException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.PASSWORD_REQUIRED,
			message || ResponseMessage[ResponseCode.PASSWORD_REQUIRED],
			HttpStatus.FORBIDDEN,
		);
	}
}

//Đang đợi được xài
export class InvalidPasswordFormatException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.INVALID_PASSWORD_FORMAT,
			message || ResponseMessage[ResponseCode.INVALID_PASSWORD_FORMAT],
			HttpStatus.BAD_REQUEST,
		);
	}
}

//Đang đợi được xài
export class PasswordTooWeakException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.PASSWORD_TOO_WEAK,
			message || ResponseMessage[ResponseCode.PASSWORD_TOO_WEAK],
			HttpStatus.BAD_REQUEST,
		);
	}
}

//Đang đợi được xài
export class EmailNotVerifiedException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.EMAIL_NOT_VERIFIED,
			message || ResponseMessage[ResponseCode.EMAIL_NOT_VERIFIED],
			HttpStatus.FORBIDDEN,
		);
	}
}

//Đang đợi được xài
export class PhoneNotVerifiedException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.PHONE_NOT_VERIFIED,
			message || ResponseMessage[ResponseCode.PHONE_NOT_VERIFIED],
			HttpStatus.FORBIDDEN,
		);
	}
}

export class ContactConflictException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.CONTACT_CONFLICT,
			message || ResponseMessage[ResponseCode.CONTACT_CONFLICT],
			HttpStatus.CONFLICT,
		);
	}
}

export class ContactRequiredException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.CONTACT_REQUIRED,
			message || ResponseMessage[ResponseCode.CONTACT_REQUIRED],
			HttpStatus.CONFLICT,
		);
	}
}

export class InvalidSsoTypeException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.INVALID_SSO_TYPE,
			message || ResponseMessage[ResponseCode.INVALID_SSO_TYPE],
			HttpStatus.BAD_REQUEST,
		);
	}
}

export class InvalidSsoTokenException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.INVALID_SSO_TOKEN,
			message || ResponseMessage[ResponseCode.INVALID_SSO_TOKEN],
			HttpStatus.BAD_REQUEST,
		);
	}
}

export class SsoTokenVerifyFailedException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.SSO_TOKEN_VERIFY_FAILED,
			message || ResponseMessage[ResponseCode.SSO_TOKEN_VERIFY_FAILED],
			HttpStatus.BAD_REQUEST,
		);
	}
}
