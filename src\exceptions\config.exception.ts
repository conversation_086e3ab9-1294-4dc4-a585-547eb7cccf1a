import { ResponseCode } from '@constants/response-codes';
import { HttpStatus } from '@nestjs/common';

import { AppException } from './app.exception';

export class ConfigNotFoundException extends AppException {
	constructor(key: string) {
		super(ResponseCode.NOT_FOUND, `Configuration key "${key}" not found`, HttpStatus.NOT_FOUND);
	}
}

export class InvalidConfigValueException extends AppException {
	constructor(message: string) {
		super(ResponseCode.INVALID_INPUT, message, HttpStatus.BAD_REQUEST);
	}
}

//Đang đợi được xài
export class MissingConfigCredentialsException extends AppException {
	constructor() {
		super(
			ResponseCode.INTERNAL_ERROR,
			'Missing Infisical credentials in environment variables',
			HttpStatus.INTERNAL_SERVER_ERROR,
		);
	}
}