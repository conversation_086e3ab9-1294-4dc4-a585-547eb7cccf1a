import { ResponseCode, ResponseMessage } from '@constants/response-codes';
import { HttpStatus } from '@nestjs/common';

import { AppException } from './app.exception';

//Đang đợi được xài
export class DatabaseConnectionException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.DATABASE_CONNECTION_ERROR,
			message || ResponseMessage[ResponseCode.DATABASE_CONNECTION_ERROR],
			HttpStatus.INTERNAL_SERVER_ERROR,
		);
	}
}

//Đang đợi được xài
export class DuplicateEntryException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.DUPLICATE_ENTRY,
			message || ResponseMessage[ResponseCode.DUPLICATE_ENTRY],
			HttpStatus.CONFLICT,
		);
	}
}

//Đang đợi được xài
export class ForeignKeyConstraintException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.FOREIGN_KEY_CONSTRAINT,
			message || ResponseMessage[ResponseCode.FOREIGN_KEY_CONSTRAINT],
			HttpStatus.BAD_REQUEST,
		);
	}
}

//Đang đợi được xài
export class DataIntegrityException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.DATA_INTEGRITY_ERROR,
			message || ResponseMessage[ResponseCode.DATA_INTEGRITY_ERROR],
			HttpStatus.BAD_REQUEST,
		);
	}
}