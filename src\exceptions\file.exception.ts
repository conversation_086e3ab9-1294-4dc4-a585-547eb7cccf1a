import { ResponseCode, ResponseMessage } from '@constants/response-codes';
import { HttpStatus } from '@nestjs/common';

import { AppException } from './app.exception';

//Đang đợi được xài
export class FileNotFoundException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.FILE_NOT_FOUND,
			message || ResponseMessage[ResponseCode.FILE_NOT_FOUND],
			HttpStatus.NOT_FOUND,
		);
	}
}

//Đang đợi được xài
export class FileTooLargeException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.FILE_TOO_LARGE,
			message || ResponseMessage[ResponseCode.FILE_TOO_LARGE],
			HttpStatus.BAD_REQUEST,
		);
	}
}

//Đang đợi được xài
export class InvalidFileFormatException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.INVALID_FILE_FORMAT,
			message || ResponseMessage[ResponseCode.INVALID_FILE_FORMAT],
			HttpStatus.BAD_REQUEST,
		);
	}
}

//Đang đợi được xài
export class FileUploadFailedException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.FILE_UPLOAD_FAILED,
			message || ResponseMessage[ResponseCode.FILE_UPLOAD_FAILED],
			HttpStatus.INTERNAL_SERVER_ERROR,
		);
	}
}

//Đang đợi được xài
export class FileProcessingFailedException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.FILE_PROCESSING_FAILED,
			message || ResponseMessage[ResponseCode.FILE_PROCESSING_FAILED],
			HttpStatus.INTERNAL_SERVER_ERROR,
		);
	}
}