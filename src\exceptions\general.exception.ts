import { ResponseCode, ResponseMessage } from '@constants/response-codes';
import { HttpStatus } from '@nestjs/common';

import { AppException } from './app.exception';

export class ServiceUnavailableException extends AppException {
	constructor(service: string) {
		super(
			ResponseCode.SERVICE_UNAVAILABLE,
			`${service} is currently unavailable. Please try again later.`,
			HttpStatus.SERVICE_UNAVAILABLE,
		);
	}
}

export class InternalServerErrorException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.INTERNAL_ERROR,
			message || ResponseMessage[ResponseCode.INTERNAL_ERROR],
			HttpStatus.INTERNAL_SERVER_ERROR,
		);
	}
}

export class UnauthorizedException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.UNAUTHORIZED,
			message || ResponseMessage[ResponseCode.UNAUTHORIZED],
			HttpStatus.UNAUTHORIZED,
		);
	}
}

export class ForbiddenException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.FORBIDDEN,
			message || ResponseMessage[ResponseCode.FORBIDDEN],
			HttpStatus.FORBIDDEN,
		);
	}
}

export class BadRequestException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.BAD_REQUEST,
			message || ResponseMessage[ResponseCode.BAD_REQUEST],
			HttpStatus.BAD_REQUEST,
		);
	}
}

export class NotFoundException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.NOT_FOUND,
			message || ResponseMessage[ResponseCode.NOT_FOUND],
			HttpStatus.NOT_FOUND,
		);
	}
}

export class ConflictException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.CONFLICT,
			message || ResponseMessage[ResponseCode.CONFLICT],
			HttpStatus.CONFLICT,
		);
	}
}

export class TooManyRequestsException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.TOO_MANY_REQUESTS,
			message || ResponseMessage[ResponseCode.TOO_MANY_REQUESTS],
			HttpStatus.TOO_MANY_REQUESTS,
		);
	}
}

export class UnprocessableEntityException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.UNPROCESSABLE_ENTITY,
			message || ResponseMessage[ResponseCode.UNPROCESSABLE_ENTITY],
			HttpStatus.UNPROCESSABLE_ENTITY,
		);
	}
}

export class PageTypeException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.BAD_REQUEST,
			message || ResponseMessage[ResponseCode.BAD_REQUEST],
			HttpStatus.BAD_REQUEST,
		);
	}
}

export class InvalidDtoException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.INVALID_INPUT,
			message || ResponseMessage[ResponseCode.INVALID_INPUT],
			HttpStatus.BAD_REQUEST,
		);
	}
}

export class TypeException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.INVALID_INPUT,
			message || ResponseMessage[ResponseCode.INVALID_INPUT],
			HttpStatus.BAD_REQUEST,
		);
	}
}

export class InvalidUrlException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.INVALID_INPUT,
			message || ResponseMessage[ResponseCode.INVALID_INPUT],
			HttpStatus.BAD_REQUEST,
		);
	}
}

export class InvalidDateException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.INVALID_INPUT,
			message || ResponseMessage[ResponseCode.INVALID_INPUT],
			HttpStatus.BAD_REQUEST,
		);
	}
}

export class InvalidGameIdException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.INVALID_INPUT,
			message || ResponseMessage[ResponseCode.INVALID_INPUT],
			HttpStatus.BAD_REQUEST,
		);
	}
}

export class UtilException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.INTERNAL_ERROR,
			message || ResponseMessage[ResponseCode.INTERNAL_ERROR],
			HttpStatus.INTERNAL_SERVER_ERROR,
		);
	}
}