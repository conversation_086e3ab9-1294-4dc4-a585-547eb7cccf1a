import { ResponseCode, ResponseMessage } from '@constants/response-codes';
import { HttpStatus } from '@nestjs/common';

import { AppException } from './app.exception';

//Đang đợi được xài
export class PaymentFailedException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.PAYMENT_FAILED,
			message || ResponseMessage[ResponseCode.PAYMENT_FAILED],
			HttpStatus.BAD_REQUEST,
		);
	}
}

//Đang đợi được xài
export class InsufficientBalanceException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.INSUFFICIENT_BALANCE,
			message || ResponseMessage[ResponseCode.INSUFFICIENT_BALANCE],
			HttpStatus.BAD_REQUEST,
		);
	}
}

//Đang đợi được xài
export class InvalidPaymentMethodException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.INVALID_PAYMENT_METHOD,
			message || ResponseMessage[ResponseCode.INVALID_PAYMENT_METHOD],
			HttpStatus.BAD_REQUEST,
		);
	}
}

//Đang đợi được xài
export class InvalidAmountException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.INVALID_AMOUNT,
			message || ResponseMessage[ResponseCode.INVALID_AMOUNT],
			HttpStatus.BAD_REQUEST,
		);
	}
}

//Đang đợi được xài
export class PaymentAlreadyProcessedException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.PAYMENT_ALREADY_PROCESSED,
			message || ResponseMessage[ResponseCode.PAYMENT_ALREADY_PROCESSED],
			HttpStatus.CONFLICT,
		);
	}
}

//Đang đợi được xài
export class PaymentCancelledException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.PAYMENT_CANCELLED,
			message || ResponseMessage[ResponseCode.PAYMENT_CANCELLED],
			HttpStatus.BAD_REQUEST,
		);
	}
}

//Đang đợi được xài
export class PaymentExpiredException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.PAYMENT_EXPIRED,
			message || ResponseMessage[ResponseCode.PAYMENT_EXPIRED],
			HttpStatus.BAD_REQUEST,
		);
	}
}

//Đang đợi được xài
export class MinimumAmountNotMetException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.MINIMUM_AMOUNT_NOT_MET,
			message || ResponseMessage[ResponseCode.MINIMUM_AMOUNT_NOT_MET],
			HttpStatus.BAD_REQUEST,
		);
	}
}

//Đang đợi được xài
export class MaximumAmountExceededException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.MAXIMUM_AMOUNT_EXCEEDED,
			message || ResponseMessage[ResponseCode.MAXIMUM_AMOUNT_EXCEEDED],
			HttpStatus.BAD_REQUEST,
		);
	}
}

//Đang đợi được xài
export class PaymentMethodNotSupportedException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.PAYMENT_METHOD_NOT_SUPPORTED,
			message || ResponseMessage[ResponseCode.PAYMENT_METHOD_NOT_SUPPORTED],
			HttpStatus.BAD_REQUEST,
		);
	}
}