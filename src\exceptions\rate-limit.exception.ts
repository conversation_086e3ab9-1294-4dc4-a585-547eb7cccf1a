import { ResponseCode, ResponseMessage } from '@constants/response-codes';
import { HttpStatus } from '@nestjs/common';

import { AppException } from './app.exception';

export class RateLimitExceededException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.RATE_LIMIT_EXCEEDED,
			message || ResponseMessage[ResponseCode.RATE_LIMIT_EXCEEDED],
			HttpStatus.TOO_MANY_REQUESTS,
		);
	}
}

//Đang đợi được xài
export class DailyLimitExceededException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.DAILY_LIMIT_EXCEEDED,
			message || ResponseMessage[ResponseCode.DAILY_LIMIT_EXCEEDED],
			HttpStatus.TOO_MANY_REQUESTS,
		);
	}
}

//Đang đợi được xài
export class MonthlyLimitExceededException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.MONTHLY_LIMIT_EXCEEDED,
			message || ResponseMessage[ResponseCode.MONTHLY_LIMIT_EXCEEDED],
			HttpStatus.TOO_MANY_REQUESTS,
		);
	}
}

//Đang đợi được xài
export class TwoFactorRequiredException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.TWO_FACTOR_REQUIRED,
			message || ResponseMessage[ResponseCode.TWO_FACTOR_REQUIRED],
			HttpStatus.FORBIDDEN,
		);
	}
}

export class InvalidOtpCodeException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.INVALID_OTP_CODE,
			message || ResponseMessage[ResponseCode.INVALID_OTP_CODE],
			HttpStatus.BAD_REQUEST,
		);
	}
}

export class OtpAttemptExceedException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.OTP_ATTEMPTS_EXCEEDED,
			message || ResponseMessage[ResponseCode.OTP_ATTEMPTS_EXCEEDED],
			HttpStatus.BAD_REQUEST,
		);
	}
}

export class OtpExpiredException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.OTP_EXPIRED,
			message || ResponseMessage[ResponseCode.OTP_EXPIRED],
			HttpStatus.BAD_REQUEST,
		);
	}
}