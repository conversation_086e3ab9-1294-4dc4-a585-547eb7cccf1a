import { ResponseCode, ResponseMessage } from '@constants/response-codes';
import { HttpStatus } from '@nestjs/common';

import { AppException } from './app.exception';

export class UserNotFoundException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.USER_NOT_FOUND,
			message || ResponseMessage[ResponseCode.USER_NOT_FOUND],
			HttpStatus.NOT_FOUND,
		);
	}
}

export class UserAlreadyExistsException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.USER_ALREADY_EXISTS,
			message || ResponseMessage[ResponseCode.USER_ALREADY_EXISTS],
			HttpStatus.CONFLICT,
		);
	}
}

export class UserInactiveException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.USER_INACTIVE,
			message || ResponseMessage[ResponseCode.USER_INACTIVE],
			HttpStatus.FORBIDDEN,
		);
	}
}

export class UserProfileNotFoundException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.USER_PROFILE_NOT_FOUND,
			message || ResponseMessage[ResponseCode.USER_PROFILE_NOT_FOUND],
			HttpStatus.NOT_FOUND,
		);
	}
}

//Đang đợi được xài
export class UserProfileIncompleteException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.USER_PROFILE_INCOMPLETE,
			message || ResponseMessage[ResponseCode.USER_PROFILE_INCOMPLETE],
			HttpStatus.BAD_REQUEST,
		);
	}
}

export class InvalidUserIdException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.INVALID_USER_ID,
			message || ResponseMessage[ResponseCode.INVALID_USER_ID],
			HttpStatus.BAD_REQUEST,
		);
	}
}

export class InvalidUserTypeException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.INVALID_USER_TYPE,
			message || ResponseMessage[ResponseCode.INVALID_USER_TYPE],
			HttpStatus.BAD_REQUEST,
		);
	}
}

//Đang đợi được xài
export class UserBlockedException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.USER_BLOCKED,
			message || ResponseMessage[ResponseCode.USER_BLOCKED],
			HttpStatus.FORBIDDEN,
		);
	}
}

//Đang đợi được xài
export class UserDeletedException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.USER_DELETED,
			message || ResponseMessage[ResponseCode.USER_DELETED],
			HttpStatus.GONE,
		);
	}
}

export class UserNotVerifiedException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.USER_NOT_VERIFIED,
			message || ResponseMessage[ResponseCode.USER_NOT_VERIFIED],
			HttpStatus.FORBIDDEN,
		);
	}
}

export class AccountAlreadyLinkedException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.ACCOUNT_ALREADY_LINKED,
			message || ResponseMessage[ResponseCode.ACCOUNT_ALREADY_LINKED],
			HttpStatus.CONFLICT,
		);
	}
}

export class UserNotLinkedException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.USER_NOT_LINKED,
			message || ResponseMessage[ResponseCode.USER_NOT_LINKED],
			HttpStatus.CONFLICT,
		);
	}
}

//Đang đợi được xài
export class UserEmailNotVerifiedException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.USER_EMAIL_NOT_VERIFIED,
			message || ResponseMessage[ResponseCode.USER_EMAIL_NOT_VERIFIED],
			HttpStatus.FORBIDDEN,
		);
	}
}

//Đang đợi được xài
export class UserPhoneNotVerifiedException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.USER_PHONE_NOT_VERIFIED,
			message || ResponseMessage[ResponseCode.USER_PHONE_NOT_VERIFIED],
			HttpStatus.FORBIDDEN,
		);
	}
}
