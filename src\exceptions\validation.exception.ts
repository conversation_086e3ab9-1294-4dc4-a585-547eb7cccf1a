import { ResponseCode } from '@constants/response-codes';
import { HttpStatus } from '@nestjs/common';

import { AppException } from './app.exception';

export class ValidationException extends AppException {
	constructor(message?: string) {
		super(ResponseCode.VALIDATION_ERROR, message, HttpStatus.BAD_REQUEST);
	}
}

//Đang đợi được xài
export class InvalidFormatException extends AppException {
	constructor(message?: string) {
		super(ResponseCode.INVALID_INPUT, message, HttpStatus.BAD_REQUEST);
	}
}
