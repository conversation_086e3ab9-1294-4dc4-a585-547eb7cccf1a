import { ResponseCode, ResponseMessage } from '@constants/response-codes';
import { HttpStatus } from '@nestjs/common';

import { AppException } from './app.exception';

//Đang đợi được xài
export class VipTierNotFoundException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.VIP_TIER_NOT_FOUND,
			message || ResponseMessage[ResponseCode.VIP_TIER_NOT_FOUND],
			HttpStatus.NOT_FOUND,
		);
	}
}

//Đang đợi được xài
export class VipBenefitNotFoundException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.VIP_BENEFIT_NOT_FOUND,
			message || ResponseMessage[ResponseCode.VIP_BENEFIT_NOT_FOUND],
			HttpStatus.NOT_FOUND,
		);
	}
}

//Đang đợi được xài
export class VipRequirementNotMetException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.VIP_REQUIREMENT_NOT_MET,
			message || ResponseMessage[ResponseCode.VIP_REQUIREMENT_NOT_MET],
			HttpStatus.BAD_REQUEST,
		);
	}
}

//Đang đợi được xài
export class VipTierAlreadyExistsException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.VIP_TIER_ALREADY_EXISTS,
			message || ResponseMessage[ResponseCode.VIP_TIER_ALREADY_EXISTS],
			HttpStatus.CONFLICT,
		);
	}
}

//Đang đợi được xài
export class VipBenefitAlreadyExistsException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.VIP_BENEFIT_ALREADY_EXISTS,
			message || ResponseMessage[ResponseCode.VIP_BENEFIT_ALREADY_EXISTS],
			HttpStatus.CONFLICT,
		);
	}
}

//Đang đợi được xài
export class InvalidVipUpgradeException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.INVALID_VIP_UPGRADE,
			message || ResponseMessage[ResponseCode.INVALID_VIP_UPGRADE],
			HttpStatus.BAD_REQUEST,
		);
	}
}