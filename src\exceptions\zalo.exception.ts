import { ResponseCode, ResponseMessage } from '@constants/response-codes';
import { HttpStatus } from '@nestjs/common';

import { AppException } from './app.exception';

export class ZaloOAuthFailedException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.ZALO_OAUTH_FAILED,
			message || ResponseMessage[ResponseCode.ZALO_OAUTH_FAILED],
			HttpStatus.BAD_REQUEST,
		);
	}
}

export class ZaloGetExchangeTokenFailedException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.ZALO_GET_EXCHANGE_TOKEN_FAILED,
			message || ResponseMessage[ResponseCode.ZALO_GET_EXCHANGE_TOKEN_FAILED],
			HttpStatus.INTERNAL_SERVER_ERROR,
		);
	}
}

export class ZaloMissingRefreshTokenException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.ZALO_MISSING_REFRESH_TOKEN,
			message || ResponseMessage[ResponseCode.ZALO_MISSING_REFRESH_TOKEN],
			HttpStatus.INTERNAL_SERVER_ERROR,
		);
	}
}

export class ZaloMissingAccessTokenException extends AppException {
	constructor(message?: string) {
		super(
			ResponseCode.ZALO_MISSING_ACCESS_TOKEN,
			message || ResponseMessage[ResponseCode.ZALO_MISSING_ACCESS_TOKEN],
			HttpStatus.INTERNAL_SERVER_ERROR,
		);
	}
}