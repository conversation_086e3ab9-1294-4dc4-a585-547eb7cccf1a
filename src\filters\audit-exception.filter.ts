// src/common/filters/audit-exception.filter.ts;
import { getIp } from '@common/utils';
import { 
	type ArgumentsHost, 
	Catch, 
	type ExceptionFilter, 
	HttpException, 
	HttpStatus,
	Logger, 
} from '@nestjs/common';
import type { Request, Response } from 'express';

import { AuditLogCategory, AuditLogLevel } from '../constants/audit-log';
import { AuditLogService } from '../modules/audit-log/audit-log.service';
import { UserAccountEntity } from '../modules/user/user-account.entity';

@Catch()
export class AuditExceptionFilter implements ExceptionFilter {
	private readonly logger = new Logger(AuditExceptionFilter.name);

	constructor(private auditLogService: AuditLogService) {}

	catch(exception: any, host: ArgumentsHost): void {
		const ctx = host.switchToHttp();
		const response = ctx.getResponse<Response>();
		const request = ctx.getRequest<Request>();
		const user = request['user'] as UserAccountEntity;

		const status = exception instanceof HttpException 
			? exception.getStatus() 
			: HttpStatus.INTERNAL_SERVER_ERROR;

		const message = exception instanceof HttpException
			? exception.getResponse()
			: 'Internal server error';

		// Log the exception
		this.auditLogService.logAction({
			userId: user?.userId,
			action: 'system_exception',
			category: AuditLogCategory.SYSTEM,
			level: status >= 500 ? AuditLogLevel.CRITICAL : AuditLogLevel.ERROR,
			success: false,
			errorMessage: exception.message,
			ip: getIp(request),
			userAgent: request.get('User-Agent'),
			sessionId: request.get('X-Session-ID'),
			requestId: request.get('X-Request-ID'),
			context: {
				path: request.url,
				method: request.method,
				statusCode: status,
				exception: exception.name,
				stack: exception.stack,
			},
			metadata: {
				httpMethod: request.method,
				endpoint: request.url,
				statusCode: status,
				errorCode: exception.code,
			},
			immediate: status >= 500, // Process critical errors immediately
		}).catch(error => {
			this.logger.error('Failed to log exception', error);
		});

		response.status(status).json({
			statusCode: status,
			timestamp: new Date().toISOString(),
			path: request.url,
			message: typeof message === 'string' ? message : (message as any).message,
			requestId: request.get('X-Request-ID'),
		});
	}
}