import { ResponseDto } from '@common/dto/response.dto';
import { ResponseCode, ResponseMessage } from '@constants/response-codes';
import type { ArgumentsHost, ExceptionFilter } from '@nestjs/common';
import { Catch, UnprocessableEntityException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import type { Response } from 'express';

@Catch(UnprocessableEntityException)
export class UnprocessableEntityFilter
implements ExceptionFilter<UnprocessableEntityException> {
	constructor(public reflector: Reflector) {}

	catch(exception: UnprocessableEntityException, host: ArgumentsHost): void {
		const ctx = host.switchToHttp();
		const response = ctx.getResponse<Response>();
		const statusCode = exception.getStatus();

		// Luôn trả về {} cho trường data khi có lỗi
		response.status(statusCode).json(
			new ResponseDto(
				ResponseCode.VALIDATION_ERROR,
				ResponseMessage[ResponseCode.VALIDATION_ERROR],
				{},
			),
		);
	}
}