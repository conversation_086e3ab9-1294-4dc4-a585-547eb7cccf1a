import { ResponseDto } from '@common/dto/response.dto';
import { ResponseCode, ResponseMessage } from '@constants/response-codes';
import {
	type ArgumentsHost,
	Catch,
	type ExceptionFilter,
	HttpException,
	HttpStatus,
	Logger,
} from '@nestjs/common';
import { AppException } from 'exceptions/app.exception';
import type { Response } from 'express';


@Catch()
export class HttpExceptionFilter implements ExceptionFilter {
	private readonly logger = new Logger(HttpExceptionFilter.name);

	catch(exception: unknown, host: ArgumentsHost) {
		const ctx = host.switchToHttp();
		const response = ctx.getResponse<Response>();
		const request = ctx.getRequest();

		let statusCode: ResponseCode;
		let message: string;
		let httpStatus: number;

		if (exception instanceof AppException) {
			// Handle custom AppException
			statusCode = exception.getErrorCode();
			message = exception.message;
			httpStatus = exception.getStatus();
      
			this.logger.warn(
				`AppException: ${statusCode} - ${message} - ${request.method} ${request.url}`,
			);
		} else if (exception instanceof HttpException) {
			// Handle standard HttpException
			httpStatus = exception.getStatus();
			const exceptionResponse = exception.getResponse();
      
			// Map HTTP status to ResponseCode
			statusCode = this.mapHttpStatusToResponseCode(httpStatus);
      
			if (typeof exceptionResponse === 'object' && exceptionResponse !== null && 'message' in exceptionResponse) {
				const messageProperty = (exceptionResponse as { message?: unknown }).message;
				message = Array.isArray(messageProperty) 
					? messageProperty.join(', ')
					: String(messageProperty);
			} else {
				message = exception.message || ResponseMessage[statusCode];
			}
      
			this.logger.warn(
				`HttpException: ${httpStatus} - ${message} - ${request.method} ${request.url}`,
			);
		} else {
			// Handle unknown exceptions
			statusCode = ResponseCode.INTERNAL_ERROR;
			message = ResponseMessage[ResponseCode.INTERNAL_ERROR];
			httpStatus = HttpStatus.INTERNAL_SERVER_ERROR;
      
			this.logger.error(
				`Unhandled Exception: ${exception} - ${request.method} ${request.url}`,
				exception instanceof Error ? exception.stack : undefined,
			);
		}

		const responseBody = ResponseDto.error(statusCode, message);

		response.status(httpStatus).json(responseBody);
	}

	private mapHttpStatusToResponseCode(status: number): ResponseCode {
		switch (status) {
			case HttpStatus.BAD_REQUEST:
				return ResponseCode.BAD_REQUEST;
			case HttpStatus.UNAUTHORIZED:
				return ResponseCode.UNAUTHORIZED;
			case HttpStatus.FORBIDDEN:
				return ResponseCode.FORBIDDEN;
			case HttpStatus.NOT_FOUND:
				return ResponseCode.NOT_FOUND;
			case HttpStatus.CONFLICT:
				return ResponseCode.CONFLICT;
			case HttpStatus.UNPROCESSABLE_ENTITY:
				return ResponseCode.UNPROCESSABLE_ENTITY;
			case HttpStatus.TOO_MANY_REQUESTS:
				return ResponseCode.TOO_MANY_REQUESTS;
			default:
				return ResponseCode.INTERNAL_ERROR;
		}
	}
}