import { ResponseDto } from '@common/dto/response.dto';
import { ResponseCode, ResponseMessage } from '@constants/response-codes';
import type { ArgumentsHost, ExceptionFilter } from '@nestjs/common';
import { Catch, HttpStatus } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import type { Response } from 'express';
import { QueryFailedError } from 'typeorm';

import { constraintErrors } from './constraint-errors';

@Catch(QueryFailedError)
export class QueryFailedFilter implements ExceptionFilter<QueryFailedError> {
	constructor(public reflector: Reflector) {}

	catch(
		exception: QueryFailedError & { constraint?: string },
		host: ArgumentsHost,
	) {
		const ctx = host.switchToHttp();
		const response = ctx.getResponse<Response>();

		const status = exception.constraint?.startsWith('UQ')
			? HttpStatus.CONFLICT
			: HttpStatus.INTERNAL_SERVER_ERROR;

		// Xác đ<PERSON>nh statusCode tương ứng
		const statusCode = exception.constraint?.startsWith('UQ')
			? ResponseCode.CONFLICT
			: ResponseCode.INTERNAL_ERROR;

		// Luôn trả về {} cho trường data khi có lỗi
		let message: string = ResponseMessage[statusCode] || 'Internal Server Error';
		if (exception.constraint && constraintErrors[exception.constraint]) {
			message = constraintErrors[exception.constraint] || message;
		}
			
		response.status(status).json(
			new ResponseDto(
				statusCode,
				message,
				{},
			),
		);
	}
}