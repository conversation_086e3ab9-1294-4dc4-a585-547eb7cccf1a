import type { RoleType } from '@constants/role-type';
import type { UserAccountEntity } from '@modules/user/user-account.entity';
import type { CanActivate, ExecutionContext } from '@nestjs/common';
import { Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';

import { Roles } from '../decorators/roles.decorator.ts';

@Injectable()
export class RolesGuard implements CanActivate {
	constructor(private readonly reflector: Reflector) {}

	canActivate(context: ExecutionContext): boolean {
		const roles = this.reflector.get<RoleType[] | undefined>(
			Roles,
			context.getHandler(),
		);

		// If no roles are specified on the route, allow access (authentication is still enforced by AuthGuard)
		if (!roles || roles.length === 0) {
			return true;
		}

		const request = context
			.switchToHttp()
			.getRequest<{ user: UserAccountEntity & { role?: RoleType } }>();
		const user = request.user;

		if (!user) {
			return false;
		}

		// Allow public strategy
		if ((user as any)?.[Symbol.for('isPublic')]) {
			return true;
		}

		const userRole = (user as any).role as RoleType | undefined;
		return !!userRole && roles.includes(userRole);
	}
}
