import { ResponseCode, ResponseMessage } from '@constants/response-codes';
import { RAW_RESPONSE_KEY } from '@decorators/raw-response.decorator';
import {
	type CallHandler,
	type ExecutionContext,
	Injectable,
	type NestInterceptor,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { map } from 'rxjs/operators';

import { ResponseDto } from '../common/dto/response.dto';

@Injectable()
export class ResponseInterceptor<T>
implements NestInterceptor<T, ResponseDto<T>> {
	constructor(private readonly reflector: Reflector) {}

	intercept(context: ExecutionContext, next: CallHandler) {
		const isRawResponse = this.reflector.get<boolean>(
			RAW_RESPONSE_KEY,
			context.getHandler(),
		);

		// Check if this is an HTTP context
		const httpContext = context.switchToHttp();
		const response = httpContext.getResponse();

		// If it's a raw response or the response has already been sent (e.g. redirects), bypass interceptor
		if (isRawResponse || response.headersSent) {
			return next.handle();
		}

		return next.handle().pipe(
			map((data) => {
				// Nếu đã là ResponseDto thì trả về nguyên dữ liệu
				if (data instanceof ResponseDto) {
					return data;
				}

				// Trường hợp success mặc định
				return new ResponseDto(
					ResponseCode.SUCCESS,
					ResponseMessage[ResponseCode.SUCCESS],
					data,
				);
			}),
		);
	}
}