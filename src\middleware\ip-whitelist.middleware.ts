import { getIp } from '@common/utils';
import { SysConfigService } from '@modules/sys-config/sys-config.service';
import { ForbiddenException, Injectable, type NestMiddleware } from '@nestjs/common';
import type { NextFunction, Request, Response } from 'express';

@Injectable()
export class IpWhitelistMiddleware implements NestMiddleware {
	constructor(
		private readonly sysConfig: SysConfigService,
	) {}

	async use(req: Request, _res: Response, next: NextFunction) {
		const maintenanceMode = await this.sysConfig.getValue<boolean>('MAINTENANCE_MODE') || false;
		if (!maintenanceMode) return next();

		const ipWhitelist = await this.sysConfig.getValue<string[]>('IP_WHITELIST') || [];
		const clientIp = getIp(req);
		console.info(ipWhitelist, clientIp);

		if (!ipWhitelist.includes(clientIp)) {
			throw new ForbiddenException(`Access denied from IP ${clientIp} during maintenance mode`);
		}

		return next();
	}
}
