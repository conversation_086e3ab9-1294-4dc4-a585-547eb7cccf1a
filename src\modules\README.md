# FS Player Service - Modules Overview

## 📋 Overview

This directory contains all the core modules of the FS Player Service. Each module is designed to handle specific functionality and follows a modular architecture pattern for maintainability, scalability, and separation of concerns.

## 🏗️ Module Architecture

### Module Structure
Each module follows a consistent structure:
```
module-name/
├── README.md                 # Module documentation
├── module-name.module.ts     # NestJS module definition
├── module-name.service.ts    # Business logic service
├── module-name.controller.ts # API endpoints controller
├── entities/                 # Database entities
├── dtos/                     # Data Transfer Objects
└── interfaces/               # TypeScript interfaces
```

### Design Principles
- **Single Responsibility** - Each module handles one specific domain
- **Loose Coupling** - Modules communicate through well-defined interfaces
- **High Cohesion** - Related functionality is grouped together
- **Dependency Injection** - Services are injected for testability
- **Configuration Driven** - Behavior controlled through configuration

## 📚 Available Modules

### 🔐 [Authentication Module](./auth/README.md)
**Purpose**: User authentication, authorization, and session management

**Key Features**:
- Local and social authentication (Facebook, Google, Apple)
- JWT token management with refresh tokens
- Password reset and account verification
- Rate limiting and security features
- Role-based access control (USER/ADMIN)

**Main Endpoints**: `/auth/login`, `/auth/register`, `/auth/logout`

---

### 👤 [User Module](./user/README.md)
**Purpose**: User account and profile management

**Key Features**:
- User account CRUD operations
- Profile management with customizable information
- Quickplay account system for anonymous gaming
- Account linking and conversion
- User search and filtering

**Main Endpoints**: `/users/profile`, `/users/me`, `/users/quickplay`

---



### 📱 [Zalo OAuth Module](./zalo-oauth/README.md)
**Purpose**: Zalo social authentication integration

**Key Features**:
- Complete OAuth 2.0 PKCE flow
- Zalo user profile integration
- Vietnamese market optimization
- Mobile app deep link support
- Dynamic configuration management

**Main Endpoints**: `/zalo/auth`, `/zalo/callback`, `/zalo/profile`

---

### 📧 [OTP Module](./otp/README.md)
**Purpose**: One-Time Password generation and verification

**Key Features**:
- Email and SMS OTP delivery
- Rate limiting and security controls
- Configurable OTP settings
- Multi-purpose OTP support (verification, password reset)
- Admin-only access control

**Main Endpoints**: `/otp/send`, `/otp/verify` (Admin Only)

---

### ⚙️ [System Configuration Module](./sys-config/README.md)
**Purpose**: Dynamic system configuration management

**Key Features**:
- Runtime configuration updates
- Category-based organization
- Type-safe configuration access
- Redis caching for performance
- Admin-only management interface

**Main Endpoints**: `/sys-config`, `/sys-config/category/:category` (Admin Only)

---

### 🏥 [Health Checker Module](./health-checker/README.md)
**Purpose**: System health monitoring and status checking

**Key Features**:
- Database and Redis connectivity checks
- Memory and disk usage monitoring
- Prometheus metrics integration
- Kubernetes health probes
- Load balancer health endpoints

**Main Endpoints**: `/health`, `/health/detailed`, `/health/metrics`

---


### 👨‍💼 [Admin Module](./admin/README.md)
**Purpose**: Administrative functionality and system management

**Key Features**:
- Admin account and role management
- User administration and moderation
- System configuration and monitoring
- Bulk operations and reporting
- Permission-based access control

**Main Endpoints**: `/admin/users`, `/admin/system/stats`, `/admin/roles`

## 🔗 Module Dependencies

### Dependency Graph
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│    Auth     │────│    User     │────│   Payment   │
└─────────────┘    └─────────────┘    └─────────────┘
       │                  │                  │
       │                  │                  │
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│     OTP     │    │    Game     │    │ Audit Log  │
└─────────────┘    └─────────────┘    └─────────────┘
       │                  │                  │
       │                  │                  │
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ Sys-Config  │────│   Admin     │────│Health Check │
└─────────────┘    └─────────────┘    └─────────────┘
       │                  │
       │                  │
┌─────────────┐    ┌─────────────┐
│Zalo OAuth   │    │   Shared    │
└─────────────┘    └─────────────┘
```

### Core Dependencies
- **Sys-Config**: Used by most modules for dynamic configuration
- **Audit Log**: Used by all modules for activity tracking
- **User**: Central to authentication, payment, and game modules
- **Auth**: Provides authentication for protected endpoints

## 🛡️ Security Considerations

### Access Control
- **Public Endpoints**: Health checks, authentication
- **User Endpoints**: Require valid JWT token
- **Admin Endpoints**: Require admin role and specific permissions

### Data Protection
- **Encryption**: Sensitive data encrypted at rest
- **Validation**: All inputs validated and sanitized
- **Rate Limiting**: API endpoints protected against abuse
- **Audit Trail**: All actions logged for security monitoring

## 📊 Performance Optimization

### Caching Strategy
- **Redis**: Used for session storage, OTP storage, and configuration caching
- **Database**: Optimized queries with proper indexing
- **API**: Response caching for frequently accessed data

### Monitoring
- **Health Checks**: Continuous system health monitoring
- **Metrics**: Prometheus metrics for operational insights
- **Logging**: Structured logging for debugging and analysis

## 🚀 Getting Started

### Module Development
1. **Create Module**: Use NestJS CLI to generate new module
2. **Follow Structure**: Maintain consistent module structure
3. **Add Documentation**: Create comprehensive README.md
4. **Write Tests**: Include unit and integration tests
5. **Update Dependencies**: Update module dependency graph

### Integration
1. **Import Module**: Add to main app.module.ts
2. **Configure Services**: Set up required dependencies
3. **Add Routes**: Register API endpoints
4. **Test Integration**: Verify module works with existing system

## 📝 Development Guidelines

### Code Standards
- **TypeScript**: Strict type checking enabled
- **ESLint**: Code linting and formatting
- **Prettier**: Consistent code formatting
- **Documentation**: Comprehensive inline and README documentation

### Testing Requirements
- **Unit Tests**: Minimum 80% code coverage
- **Integration Tests**: API endpoint testing
- **E2E Tests**: Critical user flow testing
- **Performance Tests**: Load and stress testing

### Security Requirements
- **Input Validation**: All inputs validated using DTOs
- **Authentication**: Protected endpoints require valid tokens
- **Authorization**: Role-based access control implemented
- **Audit Logging**: All significant actions logged

## 🔧 Configuration Management

### Environment Variables
Each module uses environment variables for configuration:
```bash
# Database
DB_HOST=localhost
DB_PORT=5432

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379

# JWT
JWT_SECRET=your_jwt_secret
JWT_EXPIRATION_TIME=3600

# External Services
ZALO_APP_ID=your_zalo_app_id
MAILER_API_HOST=http://localhost:3003
```

### Dynamic Configuration
System-wide settings managed through Sys-Config module:
- Runtime configuration updates
- Environment-specific settings
- Feature flags and toggles
- Performance tuning parameters

## 📈 Monitoring and Observability

### Metrics Collection
- **Application Metrics**: Request rates, response times, error rates
- **Business Metrics**: User registrations, payments, game sessions
- **System Metrics**: Memory usage, CPU utilization, database performance

### Alerting
- **Health Alerts**: System component failures
- **Security Alerts**: Suspicious activities, failed authentications
- **Business Alerts**: Payment failures, unusual user behavior

---

**For detailed information about each module, please refer to their individual README files.**
