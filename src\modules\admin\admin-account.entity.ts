import { AbstractEntity } from '@common/abstract.entity';
import { UseDto } from '@decorators/use-dto.decorator';
import {
	Column,
	Entity,
	Index,
	OneToMany,
	PrimaryGeneratedColumn,
} from 'typeorm';

import {
	AdminAccountDto,
	AdminAccountStatus,
} from './dtos/admin-account.dto.ts';

@Index('admin_account_pkey', ['adminId'], { unique: true })
@Index('admin_account_email_key', ['email'], { unique: true })
@Entity('admin_account', { schema: 'public' })
@UseDto(AdminAccountDto)
export class AdminAccountEntity extends AbstractEntity<AdminAccountDto> {
	@PrimaryGeneratedColumn({ type: 'bigint', name: 'admin_id' })
	adminId!: number;

	@Column('character varying', {
		name: 'email',
		nullable: true,
		unique: true,
		length: 255,
	})
	email?: string | null;

	@Column('character varying', {
		name: 'password_hash',
		length: 255,
	})
	passwordHash!: string;

	@Column('character varying', {
		name: 'status',
		length: 30,
		default: AdminAccountStatus.ACTIVE,
	})
	status!: string;

	@Column('timestamp without time zone', {
		name: 'created_at',
		default: () => 'CURRENT_TIMESTAMP',
	})
	declare createdAt: Date;

	@Column('timestamp without time zone', {
		name: 'updated_at',
		default: () => 'CURRENT_TIMESTAMP',
	})
	declare updatedAt: Date;

	@Column('timestamp without time zone', {
		name: 'last_login_at',
	})
	lastLoginAt!: Date;

	@OneToMany('AdminRoleMappingEntity', 'admin')
	adminRoleMappings!: Array<'AdminRoleMappingEntity'>;

	@OneToMany('AuditLogEntity', 'admin')
	auditLogs!: Array<'AuditLogEntity'>;
}
