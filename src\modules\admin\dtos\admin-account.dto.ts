import { AbstractDto } from '@common/dto/abstract.dto';
import {
	DateField,
	EnumFieldOptional,
	NumberField,
	StringField,
	StringFieldOptional,
} from '@decorators/field.decorators';

import type { AdminAccountEntity } from '../admin-account.entity';

export enum AdminAccountStatus {
	ACTIVE = 'active',
	INACTIVE = 'inactive',
}

export class AdminAccountDto extends AbstractDto {
	@NumberField()
	adminId!: number;

	@StringFieldOptional()
	email?: string | null;

	@StringField()
	passwordHash!: string;

	@EnumFieldOptional(() => AdminAccountStatus)
	status?: AdminAccountStatus;

	@DateField()
	createdAt: Date;

	@DateField()
	updatedAt: Date;

	@DateField()
	lastLoginAt!: Date;

	constructor(adminAccountEntity: AdminAccountEntity) {
		super(adminAccountEntity);
		this.adminId = adminAccountEntity.adminId;
		this.email = adminAccountEntity.email;
		this.passwordHash = adminAccountEntity.passwordHash;
		this.status = adminAccountEntity.status as AdminAccountStatus;
		this.createdAt = adminAccountEntity.createdAt;
		this.updatedAt = adminAccountEntity.updatedAt;
		this.lastLoginAt = adminAccountEntity.lastLoginAt;
	}
}
