import { AbstractDto } from '@common/dto/abstract.dto';
import { StringField } from '@decorators/field.decorators';

import type { RoleEntity } from '../role.entity';

export class RoleDto extends AbstractDto {
	// @NumberField()
	// roleId!: number;

	@StringField()
	roleName!: string;

	@StringField()
	description!: string;

	constructor(role: RoleEntity) {
		super(role);
		// this.roleId = role.roleId;
		this.roleName = role.roleName;
		this.description = role.description;
	}
}
