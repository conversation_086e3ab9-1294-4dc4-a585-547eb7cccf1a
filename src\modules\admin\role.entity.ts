import { AbstractEntity } from '@common/abstract.entity';
import { UseDto } from '@decorators/use-dto.decorator';
import {
	Column,
	Entity,
	Index,
	OneToMany,
	PrimaryGeneratedColumn,
} from 'typeorm';

import { RoleDto } from './dtos/role.dto';

@Index('role_pkey', ['roleId'], { unique: true })
@Index('role_role_name_key', ['roleName'], { unique: true })
@Entity('role', { schema: 'public' })
@UseDto(RoleDto)
export class RoleEntity extends AbstractEntity<RoleDto> {
	@PrimaryGeneratedColumn({ type: 'smallint', name: 'role_id' })
	roleId!: number;

	@Column('character varying', { name: 'role_name', unique: true, length: 30 })
	roleName!: string;

	@Column('character varying', {
		name: 'description',
		nullable: true,
		length: 255,
	})
	description!: string;

	@OneToMany('AdminRoleMappingEntity', 'role')
	adminRoleMappings!: Array<'AdminRoleMappingEntity'>;
}
