// src/modules/audit-log/controllers/audit-log.controller.ts
import { AuditLogCategory } from '@constants/audit-log';
import { RoleType } from '@constants/role-type';
import { AuthUser } from '@decorators/auth-user.decorator';
import { Auth } from '@decorators/http.decorators';
import { Roles } from '@decorators/roles.decorator';
import { Controller, DefaultValuePipe, Get, HttpCode, HttpStatus, ParseIntPipe, Query, UseGuards } from '@nestjs/common';
import { ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import { UnauthorizedException } from 'exceptions';

import { RolesGuard } from '../../guards/roles.guard';
import { UserAccountEntity } from '../user/user-account.entity';
import { AuditLogService } from './audit-log.service';
import { QueryAuditLogDto } from './dtos/audit-log.dto';
import { AuditLogSummaryDto, PaginatedAuditLogDto, RecentAuditLogDto } from './dtos/audit-log-response.dto';

@Controller('audit-log')
@ApiTags('Audit Log')
export class AuditLogController {
	constructor(
		private auditLogService: AuditLogService,
	) {}

	@Get('my-activity')
	@HttpCode(HttpStatus.OK)
	@Auth([RoleType.USER])
	@ApiOkResponse({
		type: PaginatedAuditLogDto,
		description: 'Get my activity logs successfully.',
		example: {
			'logs': [
				{
					'id': 1,
					'userId': 1,
					'action': 'login_success',
					'category': 'authentication',
					'level': 'info',
					'context': {
						'loginMethod': 'password',
					},
					'metadata': null,
					'ip': '127.0.0.1',
					'success': true,
					'createdAt': '2025-08-24T12:00:00.000Z',
				},
			],
			'total': 1,
		},
	})
	async getMyActivity(
		@AuthUser() user: UserAccountEntity,
		@Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
		@Query('limit', new DefaultValuePipe(20), ParseIntPipe) limit: number,
		@Query('category') category?: string,
	) {
		// Kiểm tra xem user có tồn tại không
		if (!user || !user.userId) {
			throw new UnauthorizedException('User not authenticated');
		}
		
		// Xây dựng đối tượng query với các tham số đã được xử lý
		const query: QueryAuditLogDto = {
			userId: user.userId,
			page,
			limit,
			// Chuyển logic kiểm tra category sang service layer
			category: category as AuditLogCategory | undefined,
		};
		
		return this.auditLogService.queryLogs(query);
	}

	@Get('my-summary')
	@HttpCode(HttpStatus.OK)
	@Auth([RoleType.USER])
	@ApiOkResponse({
		type: [AuditLogSummaryDto],
		description: 'Get my activity summary successfully.',
		example: [
			{
				'category': 'AUTHENTICATION',
				'count': '10',
				'errorCount': '2',
			},
		],
	})
	async getMySummary(
		@AuthUser() user: UserAccountEntity,
		@Query('days', new DefaultValuePipe(7), ParseIntPipe) days: number,
	) {
		// Kiểm tra xem user có tồn tại không
		if (!user || !user.userId) {
			throw new UnauthorizedException('User not authenticated');
		}
		
		return this.auditLogService.getUserActivitySummary(user.userId, days);
	}

	@Get('cache-stats')
	@UseGuards(RolesGuard)
	@Roles([RoleType.ADMIN])
	@HttpCode(HttpStatus.OK)
	@ApiOperation({
		summary: 'Get cache statistics (Admin Only)',
		description:
		'Get detailed cache audit log. Requires admin authentication.',
	})
	@ApiOkResponse({
		type: PaginatedAuditLogDto,
		description: 'Get audit logs successfully.',
		example: {
			'logs': [
				{
					'id': 1,
					'userId': 1,
					'action': 'user_login',
					'category': 'AUTHENTICATION',
					'level': 'INFO',
					'success': true,
					'createdAt': '2025-08-24T12:00:00.000Z',
				},
			],
			'total': 1,
		},
	})
	async queryLogs(@Query() query: QueryAuditLogDto) {
		return this.auditLogService.queryLogs(query);
	}

	@Get('recent')
	@UseGuards(RolesGuard)
	@Roles([RoleType.ADMIN])
	@HttpCode(HttpStatus.OK)
	@ApiOperation({
		summary: 'Get recent logs (Admin Only)',
		description:
		'Get detailed recent audit log. Requires admin authentication.',
	})
	@ApiOkResponse({
		type: [RecentAuditLogDto],
		description: 'Get recent audit logs successfully.',
		example: [
			{
				'id': 1,
				'userId': 1,
				'action': 'user_login',
				'category': 'AUTHENTICATION',
				'level': 'INFO',
				'success': true,
				'createdAt': '2025-08-24T12:00:00.000Z',
			},
		],
	})
	async getRecentLogs(
		@Query('limit', new DefaultValuePipe(50), ParseIntPipe) limit: number,
	) {
		return this.auditLogService.getRecentLogs(undefined, limit);
	}
}