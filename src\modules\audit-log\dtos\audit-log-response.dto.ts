
import { AuditLogCategory, AuditLogLevel } from '@constants/audit-log';
import { ApiProperty } from '@nestjs/swagger';

export class AuditLogDto {
	@ApiProperty({ example: 1 })
	id!: number;

	@ApiProperty({ example: 1, description: 'User ID associated with the log' })
	userId?: number;

	@ApiProperty({ example: 'user_login', description: 'Action performed' })
	action!: string;

	@ApiProperty({ enum: AuditLogCategory, example: AuditLogCategory.AUTHENTICATION })
	category!: AuditLogCategory;

	@ApiProperty({ enum: AuditLogLevel, example: AuditLogLevel.INFO })
	level!: AuditLogLevel;

	@ApiProperty({
		example: { reason: 'Failed login attempt' },
		description: 'Additional context data',
		required: false,
	})
	context?: Record<string, unknown>;

	@ApiProperty({
		example: { device: 'mobile' },
		description: 'Additional metadata',
		required: false,
	})
	metadata?: Record<string, unknown>;

	@ApiProperty({ example: '127.0.0.1', description: 'IP address' })
	ip?: string;

	@ApiProperty({
		example: 'Mozilla/5.0 (...)',
		description: 'User agent string',
	})
	userAgent?: string;

	@ApiProperty({
		example: 'session-id-123',
		description: 'Session ID',
		required: false,
	})
	sessionId?: string;

	@ApiProperty({
		example: 'request-id-456',
		description: 'Request ID',
		required: false,
	})
	requestId?: string;

	@ApiProperty({ example: true, description: 'Whether the action was successful' })
	success!: boolean;

	@ApiProperty({
		example: 'Invalid credentials',
		description: 'Error message if the action failed',
		required: false,
	})
	errorMessage?: string;

	@ApiProperty({
		example: '2025-08-24T12:00:00.000Z',
		description: 'Timestamp of the log',
	})
	createdAt!: Date;
}

export class PaginatedAuditLogDto {
	@ApiProperty({
		type: [AuditLogDto],
		description: 'List of audit logs for the current page',
	})
	logs!: AuditLogDto[];

	@ApiProperty({ example: 100, description: 'Total number of audit logs' })
	total!: number;
}

export class AuditLogSummaryDto {
	@ApiProperty({
		enum: AuditLogCategory,
		example: AuditLogCategory.AUTHENTICATION,
		description: 'Category of the audit log',
	})
	category!: AuditLogCategory;
  
	@ApiProperty({ example: 10, description: 'Total count for the category' })
	count!: string;
  
	@ApiProperty({ example: 2, description: 'Number of errors in the category' })
	errorCount!: string;
}

export class RecentAuditLogDto extends AuditLogDto {}
