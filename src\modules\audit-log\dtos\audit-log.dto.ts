// src/modules/audit-log/dto/audit-log.dto.ts
import { Type } from 'class-transformer';
import { IsBoolean, IsDateString, IsEnum, IsNumber, IsObject, IsOptional, IsString } from 'class-validator';

import { AuditLogCategory, AuditLogLevel } from '../../../constants/audit-log';

export class CreateAuditLogDto {
	@IsOptional()
	@IsNumber()
	userId?: number;

	@IsString()
	action!: string;

	@IsEnum(AuditLogCategory)
	category!: AuditLogCategory;

	@IsOptional()
	@IsEnum(AuditLogLevel)
	level?: AuditLogLevel;

	@IsOptional()
	@IsObject()
	context?: Record<string, unknown>;

	@IsOptional()
	@IsObject()
	metadata?: Record<string, unknown>;

	@IsOptional()
	@IsString()
	ip?: string;

	@IsOptional()
	@IsString()
	userAgent?: string;

	@IsOptional()
	@IsString()
	sessionId?: string;

	@IsOptional()
	@IsString()
	requestId?: string;

	@IsOptional()
	@IsBoolean()
	success?: boolean;

	@IsOptional()
	@IsString()
	errorMessage?: string;
}

export class QueryAuditLogDto {
	@IsOptional()
	@IsNumber()
	userId?: number;

	@IsOptional()
	@IsString()
	action?: string;

	@IsOptional()
	@IsEnum(AuditLogCategory)
	category?: AuditLogCategory;

	@IsOptional()
	@IsEnum(AuditLogLevel)
	level?: AuditLogLevel;

	@IsOptional()
	@IsDateString()
	startDate?: string;

	@IsOptional()
	@IsDateString()
	endDate?: string;

	@IsOptional()
	@Type(() => Number)
	page?: number = 1;

	@IsOptional()
	@Type(() => Number)
	limit?: number = 20;

	@IsOptional()
	@IsString()
	search?: string;

	@IsOptional()
	@IsBoolean()
	success?: boolean;
}