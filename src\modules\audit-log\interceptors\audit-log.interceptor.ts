// src/modules/audit-log/interceptors/audit-log.interceptor.ts
import { getIp } from '@common/utils';
import type {
	CallHandler,
	ExecutionContext,
	NestInterceptor,
} from '@nestjs/common';
import { Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import type { Request, Response } from 'express';
import { Observable } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';

import { AuditLogCategory, AuditLogLevel } from '../../../constants/audit-log';
import { UserAccountEntity } from '../../user/user-account.entity';
import { AuditLogService } from '../audit-log.service';

// Custom decorator to mark methods for audit logging
export const AUDIT_LOG_KEY = 'audit_log';
export const AuditLog = (action?: string, category?: AuditLogCategory) =>
	Reflector.createDecorator()({
		action,
		category,
	});

@Injectable()
export class AuditLogInterceptor implements NestInterceptor {
	constructor(
		private auditLogService: AuditLogService,
		private reflector: Reflector,
	) {}

	intercept(context: ExecutionContext, next: CallHandler): Observable<unknown> {
		const auditConfig = this.reflector.get<{
			action?: string; category?: AuditLogCategory
		}>(AUDIT_LOG_KEY, context.getHandler());
    
		if (!auditConfig) {
			return next.handle();
		}

		const request = context.switchToHttp().getRequest<Request>();
		const response = context.switchToHttp().getResponse<Response>();
		const user = request['user'] as UserAccountEntity;
		const startTime = Date.now();

		const baseLogData = {
			userId: user?.userId,
			ip: getIp(request),
			userAgent: request.get('User-Agent'),
			sessionId: request.get('X-Session-ID'),
			requestId: request.get('X-Request-ID'),
			metadata: {
				httpMethod: request.method,
				endpoint: request.url,
				duration: 0,
			},
		};

		return next.handle().pipe(
			tap((_data) => {
				const duration = Date.now() - startTime;
				this.auditLogService.logAction({
					...baseLogData,
					action: auditConfig.action || `${request.method} ${request.route?.path}`,
					category: auditConfig.category || AuditLogCategory.API,
					level: AuditLogLevel.INFO,
					success: true,
					metadata: {
						...baseLogData.metadata,
						duration,
						statusCode: response.statusCode,
					},
				});
			}),
			catchError((error: any) => {
				const duration = Date.now() - startTime;
				this.auditLogService.logAction({
					...baseLogData,
					action: auditConfig.action || `${request.method} ${request.route?.path}`,
					category: auditConfig.category || AuditLogCategory.API,
					level: AuditLogLevel.ERROR,
					success: false,
					errorMessage: error.message,
					metadata: {
						...baseLogData.metadata,
						duration,
						statusCode: error.status || 500,
						errorCode: error.code,
					},
				});
				throw error;
			}),
		);
	}
}