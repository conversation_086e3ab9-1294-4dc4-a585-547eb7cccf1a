// src/modules/audit-log/processors/audit-log.processor.ts
import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import type { Job } from 'bull';

import { AuditLogService } from '../audit-log.service';
import { CreateAuditLogDto } from '../dtos/audit-log.dto';

@Processor('audit-log')
export class AuditLogProcessor {
	private readonly logger = new Logger(AuditLogProcessor.name);

	constructor(private auditLogService: AuditLogService) {}

	@Process('process-log')
	async processAuditLog(job: Job<CreateAuditLogDto>): Promise<void> {
		try {
			await this.auditLogService.processAuditLog(job.data);
			this.logger.debug(`Processed audit log job ${job.id}`);
		} catch (error) {
			this.logger.error(`Failed to process audit log job ${job.id}`, (error as Error).stack);
			throw error;
		}
	}
}