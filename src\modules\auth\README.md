# Authentication Module

## 📋 Overview

The Authentication module handles user authentication, authorization, and session management for the FS Player Service. It provides local login, social OAuth, JWT token management, and password reset functionality.

## 🔧 Features

### Core Authentication
- **Local Authentication** - Email/phone + password login
- **Social OAuth** - Facebook and Google integration
- **JWT Token Management** - Access & refresh tokens
- **Password Reset** - OTP-based password recovery
- **Contact Verification** - Email/phone verification with OTP

### Security Features
- **Rate Limiting** - Login attempt protection
- **IP Tracking** - Security audit logging
- **Token Blacklisting** - Secure logout implementation
- **Role-Based Access** - USER/ADMIN role management

## 🚀 API Endpoints

### Authentication
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| `POST` | `/auth/login` | User login with email/phone and password | ❌ |
| `POST` | `/auth/register` | User registration with verified contact | ❌ |
| `POST` | `/auth/logout` | User logout and token invalidation | ✅ |
| `POST` | `/auth/refresh-token` | Refresh access token using refresh token | ❌ |
| `POST` | `/auth/contact-verify` | Verify email/phone and send OTP | ❌ |

### Password Management
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| `POST` | `/auth/reset-password/request` | Request password reset OTP | ❌ |
| `POST` | `/auth/reset-password` | Reset password with OTP verification | ❌ |

### Social Authentication (OAuth 2.0)
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| `GET` | `/auth/oauth2/google` | Initiate Google OAuth flow | ❌ |
| `GET` | `/auth/oauth2/google/callback` | Handle Google OAuth callback | ❌ |
| `GET` | `/auth/oauth2/facebook` | Initiate Facebook OAuth flow | ❌ |
| `GET` | `/auth/oauth2/facebook/callback` | Handle Facebook OAuth callback | ❌ |

## 📊 Request/Response Examples

### 🔐 Login
**Request:**
```http
POST /auth/login
Content-Type: application/json

{
  "username": "<EMAIL>",
  "password": "Password123!"
}
```

**Response (200 OK):**
```json
{
  "user": {
    "userId": 123,
    "username": "john_doe",
    "email": "<EMAIL>",
    "status": "ACTIVE",
    "accountType": "REGULAR",
    "userBalance": 0,
    "createdAt": "2025-08-16T10:30:00Z",
    "updatedAt": "2025-08-16T10:30:00Z",
    "lastLoginAt": "2025-08-16T10:30:00Z",
    "createdAtIp": "***********",
    "lastLoginAtIp": "***********"
  },
  "accessToken": {
    "expiresIn": 3600,
    "token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

### 📝 Contact Verification
**Request:**
```http
POST /auth/contact-verify
Content-Type: application/json

{
  "contact": "<EMAIL>"
}
```

**Response (200 OK) - New User:**
```json
{
  "status": "OTP_SENT",
  "contactType": "email",
  "message": "OTP sent to your email for registration",
  "data": {
    "userExists": false
  }
}
```

**Response (200 OK) - Existing User:**
```json
{
  "status": "LOGIN_REQUIRED",
  "contactType": "email",
  "message": "User exists. Please login with your password.",
  "data": {
    "userExists": true,
    "accountStatus": "ACTIVE"
  }
}
```

### 👤 Registration
**Request:**
```http
POST /auth/register
Content-Type: application/json

{
  "identifier": "<EMAIL>",
  "password": "Password123!",
  "verificationToken": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Response (200 OK):**
```json
{
  "status": "success",
  "message": "Account created successfully",
  "user": {
    "userId": 123,
    "username": "user_123",
    "email": "<EMAIL>",
    "status": "ACTIVE",
    "accountType": "REGULAR",
    "userBalance": 0,
    "createdAt": "2025-08-16T10:30:00Z",
    "updatedAt": "2025-08-16T10:30:00Z"
  },
  "tokens": {
    "expiresIn": 3600,
    "token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9..."
  },
  "metadata": {
    "registeredAt": "2025-08-16T10:30:00Z",
    "registrationMethod": "email_otp",
    "ipAddress": "***********"
  }
}
```

### 🔄 Refresh Token
**Request:**
```http
POST /auth/refresh-token
Content-Type: application/json

{
  "refreshToken": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Response (200 OK):**
```json
{
  "status": "success",
  "message": "Token refreshed successfully",
  "tokens": {
    "expiresIn": 3600,
    "token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9..."
  },
  "metadata": {
    "refreshedAt": "2025-08-16T10:30:00Z",
    "expiresAt": "2025-08-16T11:30:00Z"
  }
}
```

### 🔒 Password Reset Request
**Request:**
```http
POST /auth/reset-password/request
Content-Type: application/json

{
  "emailOrPhone": "<EMAIL>"
}
```

**Response (200 OK):**
```json
{
  "status": "success",
  "message": "Password reset OTP sent successfully",
  "target": "<EMAIL>",
  "expiresIn": 300
}
```

### 🔑 Password Reset
**Request:**
```http
POST /auth/reset-password
Content-Type: application/json

{
  "emailOrPhone": "<EMAIL>",
  "verificationToken": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
  "newPassword": "NewPassword123!"
}
```

**Response (200 OK):**
```json
{
  "status": "success",
  "message": "Password reset successfully",
  "metadata": {
    "resetAt": "2025-08-16T10:30:00Z",
    "method": "otp_verification"
  }
}
```

### 🚪 Logout
**Request:**
```http
POST /auth/logout
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json
```

**Response (200 OK):**
```json
{
  "status": "success",
  "message": "Logged out successfully",
  "metadata": {
    "logoutAt": "2025-08-16T10:30:00Z"
  }
}
```

## 🔒 Security Configuration

### Dynamic Settings (via sys-config)
```json
{
  "max_login_attempts": 5,
  "max_ip_login_attempts": 20,
  "session_timeout": 3600,
  "jwt_expiration_time": 3600,
  "jwt_refresh_expiration_time": 604800
}
```

### Rate Limiting
- **User-based**: 5 attempts per account
- **IP-based**: 20 attempts per IP
- **Lockout Duration**: Configurable via sys-config

## 🛡️ Authentication Strategies

### JWT Strategy
- **Algorithm**: RS256 (RSA with SHA-256)
- **Token Type**: Bearer
- **Payload**: userId, role, iat, exp

### Social Strategies
- **Facebook**: OAuth 2.0 with profile access
- **Google**: OAuth 2.0 with email/profile scope

## 📈 Usage Examples

### Basic Login Flow
```typescript
// 1. User submits credentials
const loginData = {
  email: '<EMAIL>',
  password: 'password123'
};

// 2. API validates and returns tokens
const response = await fetch('/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(loginData)
});

// 3. Store tokens for subsequent requests
const { accessToken, refreshToken } = await response.json();
localStorage.setItem('accessToken', accessToken);
```

### Protected API Calls
```typescript
// Use access token for protected endpoints
const response = await fetch('/api/protected-endpoint', {
  headers: {
    'Authorization': `Bearer ${accessToken}`,
    'Content-Type': 'application/json'
  }
});
```

### Token Refresh
```typescript
// Refresh expired access token
const response = await fetch('/auth/refresh-token', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ refreshToken })
});
```

## 🔧 Configuration

### Environment Variables
```bash
JWT_EXPIRATION_TIME=3600
JWT_REFRESH_TOKEN_EXPIRATION_TIME=2592000
JWT_PRIVATE_KEY=-----BEGIN RSA PRIVATE KEY-----...
JWT_PUBLIC_KEY=-----BEGIN PUBLIC KEY-----...
```

### Module Dependencies
- **UserService** - User management
- **OtpService** - OTP verification
- **RedisService** - Session storage
- **SysConfigService** - Dynamic configuration

## 🚨 Error Handling

### HTTP Status Codes
| Status Code | Description | Common Scenarios |
|-------------|-------------|------------------|
| `400` | Bad Request | Invalid request format, missing required fields |
| `401` | Unauthorized | Invalid credentials, expired token |
| `403` | Forbidden | Account inactive, insufficient permissions |
| `404` | Not Found | User not found, invalid endpoint |
| `409` | Conflict | Email/phone already exists |
| `429` | Too Many Requests | Rate limit exceeded |
| `500` | Internal Server Error | Server-side errors |

### Error Response Format
All error responses follow this structure:
```json
{
  "statusCode": 401,
  "message": "Invalid credentials",
  "error": "Unauthorized",
  "timestamp": "2025-08-16T10:30:00Z",
  "path": "/auth/login"
}
```

### Common Error Examples

**Invalid Credentials (401):**
```json
{
  "statusCode": 401,
  "message": "Invalid credentials",
  "error": "Unauthorized"
}
```

**Rate Limit Exceeded (429):**
```json
{
  "statusCode": 429,
  "message": "Too many login attempts. Please try again later.",
  "error": "Too Many Requests"
}
```

**Account Inactive (403):**
```json
{
  "statusCode": 403,
  "message": "Account is inactive",
  "error": "Forbidden"
}
```

**Validation Error (400):**
```json
{
  "statusCode": 400,
  "message": ["password must be longer than or equal to 8 characters"],
  "error": "Bad Request"
}
```

**User Already Exists (409):**
```json
{
  "statusCode": 409,
  "message": "User with this email already exists",
  "error": "Conflict"
}
```

## 📝 Implementation Notes

### Security Features
- **Password Hashing**: bcrypt with configurable salt rounds
- **JWT Signing**: RSA key pairs (RS256 algorithm)
- **Session Storage**: Redis for scalability and performance
- **Rate Limiting**: Dual-layer protection (user + IP based)
- **Token Blacklisting**: Secure logout with token invalidation

### Authentication Flow
1. **Contact Verification**: Email/phone validation with OTP
2. **Registration**: Account creation with verified contact
3. **Login**: Credential validation with rate limiting
4. **Token Management**: JWT access + refresh token pattern
5. **Logout**: Token blacklisting and session cleanup

### Social OAuth Integration
- **Supported Providers**: Google, Facebook
- **Flow Type**: Authorization Code with PKCE
- **Account Linking**: Automatic based on email matching
- **Fallback**: Manual account creation for new social users

### Configuration Management
- **Dynamic Settings**: Configurable via sys-config module
- **Environment Variables**: Sensitive data (keys, secrets)
- **Rate Limits**: Adjustable per environment needs

## 🔗 Related Modules

- [User Module](../user/README.md) - User management
- [OTP Module](../otp/README.md) - OTP verification
- [Sys-Config Module](../sys-config/README.md) - Dynamic configuration
