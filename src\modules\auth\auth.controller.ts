import { getIp } from '@common/utils';
import { RoleType } from '@constants/role-type';
import { ApiCommonResponse } from '@decorators/api-common-response.decorator';
import { AuthUser } from '@decorators/auth-user.decorator';
import { Auth } from '@decorators/http.decorators';
import { PublicRoute } from '@decorators/public-route.decorator';
import { RawResponse } from '@decorators/raw-response.decorator';
import { SendOtpDto, VerifyOtpDto } from '@modules/auth/dto/otp.dto';
import type { UserAccountEntity } from '@modules/user/user-account.entity';
import {
	Body,
	Controller,
	Get,
	HttpCode,
	HttpStatus,
	Ip,
	Post,
	Req,
	Res,
	UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import {
	ApiBadRequestResponse,
	ApiBody,
	ApiOperation,
	ApiTags,
	ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { ApiConfigService } from '@shared/services/api-config.service';
import type { Request, Response } from 'express';

import { AuthService } from './auth.service';
import {
	ContactVerifyDataDto,
	LoginDataDto,
	LogoutDataDto,
	RefreshTokenDataDto,
	RegisterDataDto,
	ResetPasswordDataDto,
	ResetPasswordRequestDataDto,
} from './dto/auth-response.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import { ContactVerifyDto } from './dto/contact-verify.dto';
import { SendOtpDataDto, VerifyOtpDataDto } from './dto/otp-response.dto';
import { RefreshTokenDto } from './dto/refresh-token.dto';
import {
	ResetPasswordDto,
	ResetPasswordRequestDto,
} from './dto/reset-password.dto';
import type { SocialInfoDto } from './dto/social-info.dto';
import { UserLoginDto, UserLoginSdkDto, UserLoginSdkSsoDto } from './dto/user-login.dto';
import { UserRegisterDto } from './dto/user-register.dto';

@Controller('auth')
@ApiTags('Auth')
export class AuthController {
	constructor(
		private authService: AuthService,
		private configService: ApiConfigService,
	) {}

	@Post('sdk/sso')
	@HttpCode(HttpStatus.OK)
	@PublicRoute()
	@ApiOperation({
		summary: 'SSO User login in iOS/Android apps',
		description:
      'Authenticate user with SSO cerd. Returns user info and JWT tokens',
	})
	@ApiBody({ type: UserLoginSdkSsoDto })
	@ApiCommonResponse({
		type: LoginDataDto,
		description: 'Login successful',
	})
	@ApiBadRequestResponse({
		description: 'Invalid credentials or account issues',
	})
	async userLoginSdkSso(
		@Body() userLoginSdkSsoDto: UserLoginSdkSsoDto,
		@Req() request: Request,
	): Promise<LoginDataDto> {
		return this.authService.loginSdkSso(userLoginSdkSsoDto, getIp(request));
	}

	@Post('sdk/login')
	@HttpCode(HttpStatus.OK)
	@PublicRoute()
	@ApiOperation({
		summary: 'User login in iOS/Android apps',
		description:
      'Authenticate user with email/phone and password. Returns user info and JWT tokens if verifed, else verify by OTP',
	})
	@ApiBody({ type: UserLoginSdkDto })
	@ApiCommonResponse({
		type: LoginDataDto,
		description: 'Login successful',
	})
	@ApiBadRequestResponse({
		description: 'Invalid credentials or account issues',
	})
	async userLoginSdk(
		@Body() userLoginSdkDto: UserLoginSdkDto,
		@Req() request: Request,
	): Promise<LoginDataDto | { status: 'OTP_SENT'; contact: string }> {
		return this.authService.loginSdk(userLoginSdkDto, getIp(request)) as Promise<LoginDataDto | { status: 'OTP_SENT'; contact: string }>;
	}

	@Post('sdk/register')
	@HttpCode(HttpStatus.OK)
	@PublicRoute()
	@ApiOperation({
		summary: 'User register in iOS/Android apps',
		description:
      'Authenticate user with email/phone and password. Returns user info and JWT tokens if verifed, else verify by OTP',
	})
	@ApiBody({ type: UserLoginSdkDto })
	@ApiCommonResponse({
		type: LoginDataDto,
		description: 'Login successful',
	})
	@ApiBadRequestResponse({
		description: 'Invalid credentials or account issues',
	})
	async userRegisterSdk(
		@Body() userLoginSdkDto: UserLoginSdkDto,
		@Req() request: Request,
	): Promise<LoginDataDto | { status: 'OTP_SENT'; contact: string }> {
		return this.authService.registerSdk(userLoginSdkDto, getIp(request)) as Promise<LoginDataDto | { status: 'OTP_SENT'; contact: string }>;
	}

	@Post('sdk/verify-otp')
	@ApiOperation({
		summary: 'Verify OTP code for SDK',
		description:
		'Verifies the OTP code sent to the specified email or phone number. Returns verification status and authentication tokens for next steps.',
	})
	@HttpCode(HttpStatus.OK)
	@ApiBody({ type: VerifyOtpDto })
	@ApiCommonResponse({
		type: LoginDataDto,
		description: 'OTP verified successfully',
	})
	@ApiBadRequestResponse({
		description: 'Bad request - invalid, expired, or already used OTP',
	})
	async verifyOtpSdk(
		@Body() dto: VerifyOtpDto,
		@Ip() ip: string,
	): Promise<LoginDataDto> {
		return this.authService.verifyOtpSdk(dto.target, dto.otp, ip);
	}

	@Post('login')
	@HttpCode(HttpStatus.OK)
	@PublicRoute()
	@ApiOperation({
		summary: 'User login',
		description:
      'Authenticate user with email/phone and password. Returns user info and JWT tokens.',
	})
	@ApiBody({ type: UserLoginDto })
	@ApiCommonResponse({
		type: LoginDataDto,
		description: 'Login successful',
	})
	@ApiBadRequestResponse({
		description: 'Invalid credentials or account issues',
	})
	async userLogin(
		@Body() userLoginDto: UserLoginDto,
		@Req() request: Request,
	): Promise<LoginDataDto> {
		return this.authService.login(userLoginDto, getIp(request));
	}

	@Post('verify-contact')
	@HttpCode(HttpStatus.OK)
	@PublicRoute()
	@ApiOperation({
		summary: 'Verify contact for guest',
		description:
      'Check if email or phone number exists for a guest. Sends OTP for new users.',
	})
	@ApiBody({ type: ContactVerifyDto })
	@ApiCommonResponse({
		type: ContactVerifyDataDto,
		description: 'Contact verification result',
	})
	@ApiBadRequestResponse({
		description: 'Invalid contact format or account issues',
	})
	async verifyContactForGuest(
		@Body() contactVerifyDto: ContactVerifyDto,
		@Req() request: Request,
	): Promise<ContactVerifyDataDto> {
		return this.authService.verifyContact(
			contactVerifyDto,
			getIp(request),
		);
	}

	@Post('verify-otp')
	@ApiOperation({
		summary: 'Verify OTP code',
		description:
	'Verifies the OTP code sent to the specified email or phone number. Returns verification status and optional token for next steps.',
	})
	@HttpCode(HttpStatus.OK)
	@ApiBody({ type: VerifyOtpDto })
	@ApiCommonResponse({
		type: VerifyOtpDataDto,
		description: 'OTP verified successfully',
	})
	@ApiBadRequestResponse({
		description: 'Bad request - invalid, expired, or already used OTP',
	})
	verifyOtp(@Body() dto: VerifyOtpDto): Promise<VerifyOtpDataDto> {
		return this.authService.verifyOtp(dto.target, dto.otp);
	}

	@Post('resend-otp')
	@ApiOperation({
		summary: 'Resend OTP to email or phone number',
		description:
		'Sends a one-time password to the specified email address or Vietnamese phone number. Auto-detects contact type and applies rate limiting.',
	})
	@HttpCode(HttpStatus.OK)
	@ApiBody({ type: SendOtpDto })
	@ApiCommonResponse({
		status: HttpStatus.OK,
		type: SendOtpDataDto,
		description: 'OTP sent successfully',
	})
	@ApiBadRequestResponse({
		description:
		'Bad request - invalid email/phone, rate limit exceeded, or service unavailable',
	})
	sendOtp(
		@Body() dto: SendOtpDto,
		@Ip() ip: string,
	): Promise<SendOtpDataDto> {
		return this.authService.resendOtp(dto.target, ip);
	}

	@Post('register')
	@HttpCode(HttpStatus.CREATED)
	@PublicRoute()
	@ApiOperation({
		summary: 'User registration',
		description:
      'Register new user account with verified email/phone and create authentication tokens.',
	})
	@ApiBody({ type: UserRegisterDto })
	@ApiCommonResponse({
		status: HttpStatus.CREATED,
		type: RegisterDataDto,
		description: 'Registration successful',
	})
	@ApiBadRequestResponse({
		description: 'Registration validation errors',
	})
	async userRegister(
		@Body() userRegisterDto: UserRegisterDto,
		@Req() request: Request,
	): Promise<RegisterDataDto> {
		const ip = getIp(request);
		// Add logging
		console.log(`[${new Date().toISOString()}] Registration request from IP: ${ip}`);
		console.log(`Registration data: ${JSON.stringify({
			...userRegisterDto,
			password: '****', // Mask password in logs
		})}`);
		
		return this.authService.register(userRegisterDto, ip);
	}

	@Post('reset-password/request')
	@PublicRoute()
	@HttpCode(HttpStatus.OK)
	@ApiOperation({
		summary: 'Request password reset',
		description:
      'Send OTP to email or phone for password reset. Auto-detects contact type and only sends if user exists.',
	})
	@ApiBody({ type: ResetPasswordRequestDto })
	@ApiCommonResponse({
		type: ResetPasswordRequestDataDto,
		description: 'Password reset OTP sent successfully',
	})
	@ApiBadRequestResponse({
		description: 'Invalid contact format or user not found',
	})
	async requestPasswordReset(
		@Body() resetPasswordRequestDto: ResetPasswordRequestDto,
		@Ip() ip: string,
	): Promise<ResetPasswordRequestDataDto> {
		// Add logging
		console.log(`[${new Date().toISOString()}] Password reset request from IP: ${ip}`);
		console.log(`Contact: ${resetPasswordRequestDto.emailOrPhone}`);
		
		return this.authService.requestPasswordReset(resetPasswordRequestDto, ip);
	}

	@Post('reset-password')
	@PublicRoute()
	@HttpCode(HttpStatus.OK)
	@ApiOperation({
		summary: 'Reset password',
		description:
      'Reset user password using OTP verification token and new password.',
	})
	@ApiBody({ type: ResetPasswordDto })
	@ApiCommonResponse({
		type: ResetPasswordDataDto,
		description: 'Password reset successfully',
	})
	@ApiBadRequestResponse({
		description: 'Invalid verification token, weak password, or user not found',
	})
	async resetPassword(
		@Body() resetPasswordDto: ResetPasswordDto,
		@Ip() ip: string,
	): Promise<ResetPasswordDataDto> {
		return this.authService.resetPassword(resetPasswordDto, ip);
	}

	@Post('change-password')
	@HttpCode(HttpStatus.OK)
	@Auth([RoleType.USER])
	@ApiOperation({
		summary: 'Change password',
		description:
      'Change user password using current password for verification.',
	})
	@ApiBody({ type: ChangePasswordDto })
	@ApiCommonResponse({
		type: ResetPasswordDataDto,
		description: 'Password changed successfully',
	})
	@ApiBadRequestResponse({
		description: 'Invalid current password or weak new password',
	})
	async changePassword(
		@AuthUser() user: UserAccountEntity,
		@Body() changePasswordDto: ChangePasswordDto,
		@Ip() ip: string,
	): Promise<void> {
		return this.authService.changePassword(user, changePasswordDto, ip);
	}

	@Post('refresh-token')
	@HttpCode(HttpStatus.OK)
	@PublicRoute()
	@ApiOperation({
		summary: 'Refresh access token',
		description: 'Generate new access token using valid refresh token.',
	})
	@ApiBody({ type: RefreshTokenDto })
	@ApiCommonResponse({
		type: RefreshTokenDataDto,
		description: 'Token refreshed successfully',
	})
	@ApiUnauthorizedResponse({
		description: 'Invalid or expired refresh token',
	})
	async userRefreshToken(
		@Body() refreshTokenDto: RefreshTokenDto,
	): Promise<RefreshTokenDataDto> {
		return this.authService.refreshToken(refreshTokenDto);
	}

	@Post('logout')
	@HttpCode(HttpStatus.OK)
	@Auth([RoleType.USER])
	@ApiOperation({
		summary: 'User logout',
		description:
      'Logout user, blacklist current token, clear refresh token and user session.',
	})
	@ApiCommonResponse({
		type: LogoutDataDto,
		description: 'Successfully logged out',
	})
	@ApiUnauthorizedResponse({
		description: 'Invalid or missing authentication token',
	})
	async userLogout(
		@AuthUser() user: UserAccountEntity,
		@Req() request: Request,
	): Promise<LogoutDataDto> {
		return this.authService.logout(user, request);
	}

	@Get('oauth2/google')
	@PublicRoute()
	@RawResponse()
	@UseGuards(AuthGuard('google'))
	googleLogin() {
		// Redirects to Google
	}

	@Get('oauth2/google/callback')
	@PublicRoute()
	@RawResponse()
	@UseGuards(AuthGuard('google'))
	async googleCallback(@Req() req: Request, @Res() res: Response) {
		const socialInfo = req.user as SocialInfoDto;
		const response = await this.authService.loginWithOAuth(
			socialInfo,
			getIp(req),
		);

		if (!response.ssoToken) {
			return res.redirect(
				`${this.configService.frontendUrl}/auth/callback?status=${response.userAccount.status}`,
			);
		}

		return res.redirect(
			`${this.configService.frontendUrl}/auth/callback?token=${response.ssoToken}`,
		);
	}

	@Get('oauth2/facebook')
	@PublicRoute()
	@RawResponse()
	@UseGuards(AuthGuard('facebook'))
	facebookLogin() {
		// Redirects to Facebook
	}

	@Get('oauth2/facebook/callback')
	@PublicRoute()
	@RawResponse()
	@UseGuards(AuthGuard('facebook'))
	async facebookCallback(@Req() req: Request, @Res() res: Response) {
		const socialInfo = req.user as SocialInfoDto;
		const response = await this.authService.loginWithOAuth(
			socialInfo,
			getIp(req),
		);

		if (!response.ssoToken) {
			return res.redirect(
				`${this.configService.frontendUrl}/auth/callback?status=${response.userAccount.status}`,
			);
		}

		return res.redirect(
			`${this.configService.frontendUrl}/auth/callback?token=${response.ssoToken}`,
		);
	}

	@Post('oauth2/login')
	@HttpCode(HttpStatus.OK)
	@ApiCommonResponse({
		type: LoginDataDto,
		description: 'User info with sso access token',
	})
	@Auth([RoleType.SSO_USER])
	async oauth2Login(
		@AuthUser() userLoginDto: UserLoginDto,
		@Req() request: Request,
	): Promise<LoginDataDto> {
		return this.authService.login(userLoginDto, getIp(request), true);
	}
}
