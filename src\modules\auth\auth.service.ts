import { Config<PERSON>elper } from '@common/helpers/config.helper';
import { <PERSON>rror<PERSON>elper } from '@common/helpers/error.helper.ts';
import {
	createToken,
	getContactType,
	getLoginData,
	isEmail,
	validateHash,
} from '@common/utils';
import { RoleType } from '@constants/role-type';
import { SsoType } from '@constants/sso-type.ts';
import { TokenType } from '@constants/token-type';
import { UserAccountStatus, UserAccountType } from '@constants/user';
import { QuickplayService } from '@modules/quickplay/quickplay.service.ts';
import { UserService } from '@modules/user/user.service';
import { UserAccountEntity } from '@modules/user/user-account.entity';
import { Injectable } from '@nestjs/common';
import { EventBus } from '@nestjs/cqrs';
import { JwtService } from '@nestjs/jwt';
import { ApiConfigService } from '@shared/services/api-config.service';
import { OtpService } from '@shared/services/otp.service.ts';
import { RedisService } from '@shared/services/redis.service';
import { RedisKeyManagerService } from '@shared/services/redis-key-manager.service';
import {
	AccountAlreadyExistsException,
	AccountNotActiveException,
	IncorrectPasswordException,
	InvalidPasswordException,
	InvalidRefreshTokenException,
	InvalidSsoTypeException,
	InvalidVerificationTokenException,
	PasswordRequiredException,
	QuickplayLinkException,
	TokenBlacklistedException,
	TooManyLoginAttemptsException,
	UserEmailNotVerifiedException,
	UserInactiveException,
	UserNotFoundException,
	UserPhoneNotVerifiedException,
} from 'exceptions';
import type { Request } from 'express';

import { SsoVerifyService } from '../../shared/services/sso-verify.service.ts';
import {
	ContactVerifyDataDto,
	LoginDataDto,
	LogoutDataDto,
	RefreshTokenDataDto,
	RegisterDataDto,
	ResetPasswordDataDto,
	ResetPasswordRequestDataDto,
} from './dto/auth-response.dto';
import type { ChangePasswordDto } from './dto/change-password.dto';
import { LoginUserDto } from './dto/login-user.dto.ts';
import type { SendOtpDataDto, VerifyOtpDataDto } from './dto/otp-response.dto.ts';
import type { RefreshTokenDto } from './dto/refresh-token.dto.ts';
import type { ResetPasswordDto, ResetPasswordRequestDto } from './dto/reset-password.dto.ts';
import { SocialInfoDto } from './dto/social-info.dto.ts';
import { TokenPayloadDto } from './dto/token-payload.dto.ts';
import type { UserLoginDto, UserLoginSdkDto, UserLoginSdkSsoDto } from './dto/user-login.dto.ts';
import type { UserRegisterDto } from './dto/user-register.dto.ts';
import {
	PasswordChangedEvent,
	PasswordResetEvent,
	RefreshTokenFailedEvent,
	RefreshTokenSuccessEvent,
	UserLoggedInEvent,
	UserLoggedOutEvent,
	UserRegisteredEvent,
	VerifyContactEvent,
} from './events/auth.events';

@Injectable()
export class AuthService {
	constructor(
		private jwtService: JwtService,
		private configService: ApiConfigService,
		private userService: UserService,
		private otpService: OtpService,
		private redisService: RedisService,
		private redisKeyManager: RedisKeyManagerService,
		private configHelper: ConfigHelper,
		private eventBus: EventBus,
		private ssoVerifyService: SsoVerifyService,
		private quickplayService: QuickplayService,
	) {}

	async loginSdk(userLoginDto: UserLoginSdkDto, ip: string, isSSO = false) {
		const { username: contact, password, gameKey } = userLoginDto;

		// Use common validation with rate limiting and failed login recording
		const { OTP_SENT, user } = await this.validateUserCommon(
			contact,
			password,
			ip,
			isSSO,
			true,
		);
		if (OTP_SENT) {
			await this.userService.addUserGameMapping(user.userId, gameKey);	// Re-add game mapping
			return { status: 'OTP_SENT', contact }; // Resent OTP for verification
		}

		let isVerified = user.isPhoneVerified;
		const contactType = getContactType(contact);
		if (contactType === 'email') {
			isVerified = user.isEmailVerified;
		}
		const bypassOtp = await this.configHelper.getConfigWithFallback(
			'BYPASS_OTP',
			false,
		);

		if (!isVerified) {
			if (!bypassOtp) {
				await this.otpService.sendOtp(contact, ip);
				return { status: 'OTP_SENT', contact };
			}
			if (contactType === 'email') {
				throw new UserEmailNotVerifiedException('User email not verified');
			}
			throw new UserPhoneNotVerifiedException('User phone not verified');
		}

		const token = await this.createToken({ userId: user.userId });

		await this.userService.updateLastLoginInfo(user.userId, ip);
		await this.userService.updateRefreshToken(
			user.userId,
			token.refreshToken,
		);

		await this.userService.addUserGameMapping(user.userId, gameKey);

		this.eventBus.publish(
			new UserLoggedInEvent(user.userId, ip, isSSO ? 'sso' : 'password'),
		);

		return getLoginData(new LoginUserDto(user), token, ip);
	}

	async loginSdkSso(userLoginSdkSsoDto: UserLoginSdkSsoDto, ip: string): Promise<LoginDataDto> {
		const { accessToken, ssoType, gameKey } = userLoginSdkSsoDto;
		let socialInfo = null;

		switch (ssoType) {
			case SsoType.FACEBOOK:
				socialInfo = await this.ssoVerifyService.verifyFacebookToken(accessToken);
				break;
			case SsoType.GOOGLE:
				socialInfo = await this.ssoVerifyService.verifyGoogleToken(accessToken);
				break;
			default:
				throw new InvalidSsoTypeException('Invalid SSO type');
		}

		const { userAccount } = await this.loginWithOAuth(socialInfo, ip);

		await this.userService.addUserGameMapping(userAccount.userId, gameKey);
		const token = await this.createToken({
			userId: userAccount.userId,
		});

		return getLoginData(new LoginUserDto(userAccount), token, ip);
	}

	async registerSdk(userRegisterDto: UserLoginSdkDto, ip: string) {
		const { username: contact, password, gameKey } = userRegisterDto;

		// if has quickplayId -> check quickplay account exists and use quickplayId as username
		if (userRegisterDto.quickplayId) {
			const quickplay = await this.quickplayService.checkQuickplayExists(
				userRegisterDto.quickplayId,
				gameKey,
			);
			if (!quickplay) {
				throw new QuickplayLinkException('Quickplay account not found');
			}
		}

		const { existingUser } = await this.userService.findExistingUser(contact);
		if (existingUser) {
			await this.userService.addUserGameMapping(
				existingUser.userId,
				gameKey,
			);
			throw new AccountAlreadyExistsException('Account already exists');
		}

		const tempToken = await this.jwtService.signAsync(
			{ emailOrPhone: contact, type: TokenType.SDK_REGISTRATION },
			{ expiresIn: '1h' },
		);

		const newUser = await this.userService.createUser(
			{
				identifier: contact,
				password,
				verificationToken: tempToken,
			},
			ip,
			true,
			userRegisterDto.quickplayId,
		);

		await this.userService.updateUser(newUser, {
			status: UserAccountStatus.INACTIVE,
		});

		await this.userService.addUserGameMapping(newUser.userId, gameKey);

		const bypassOtp = await this.configHelper.getConfigWithFallback(
			'BYPASS_OTP',
			false,
		);
		if (bypassOtp) {
			await this.userService.updateUser(newUser, {
				status: UserAccountStatus.ACTIVE,
			});
			const token = await this.createToken({ userId: newUser.userId });
			this.eventBus.publish(
				new UserRegisteredEvent(newUser.userId, ip, 'sdk_bypass_otp'),
			);
			return getLoginData(new LoginUserDto(newUser), token, ip);
		}

		await this.otpService.sendOtp(contact, ip);
		return { status: 'OTP_SENT', contact };
	}

	async verifyOtpSdk(contact: string, otp: string, ip: string) {
		const result = await this.otpService.verifyOtp(contact, otp);
		if (result.verified !== true) {
			throw ErrorHelper.createOtpError('invalid');
		}

		// Check user exist
		const { existingUser, contactType } = await this.userService.findExistingUser(contact);
		if (!existingUser) throw new UserNotFoundException('User not found');

		// Set ACTIVE + verified
		if (existingUser.status === UserAccountStatus.INACTIVE) {
			await this.userService.updateUser(existingUser, { status: UserAccountStatus.ACTIVE });
		}
		await this.userService.updateVerificationStatus(existingUser, contactType);

		// Create token and login data
		const token = await createToken(
			this.jwtService,
			existingUser.userId,
			this.configService.authConfig.jwtExpirationTime,
			this.configService.authConfig.jwtRefreshTokenExpirationTime,
		);
		this.eventBus.publish(
			new UserLoggedInEvent(existingUser.userId, ip, 'password'),
		);
		return getLoginData(new LoginUserDto(existingUser), new TokenPayloadDto(token), ip);
	}


	async verifyOtp(
		contact: string,
		otp: string,
	): Promise<VerifyOtpDataDto> {
		const result = await this.otpService.verifyOtp(contact, otp);
		if (result.verified !== true) {
			throw ErrorHelper.createOtpError('invalid');
		}
		// Update user status and isEmailVerified/isPhoneVerified if user exists
		const { existingUser, contactType } = await this.userService.findExistingUser(contact);
		if (existingUser) {
			await this.userService.updateVerificationStatus(existingUser, contactType);
			await this.userService.updateUser(existingUser, { status: UserAccountStatus.ACTIVE });
		}

		const verificationToken = await this.jwtService.signAsync(
			{ contact, type: TokenType.VERIFICATION_TOKEN },
			{ expiresIn: '15m' },
		);
		return {
			target: contact,
			verificationToken: verificationToken,
			verified: result.verified,
			verifiedAt: result.verifiedAt,
		};
	}

	async resendOtp(contact: string, ipAddress: string): Promise<SendOtpDataDto> {
		await this.otpService.sendOtp(contact, ipAddress);
		return { status: 'OTP_SENT', contact };
	}

	async login(
		userLoginDto: UserLoginDto,
		ip: string,
		isSSO = false,
	): Promise<LoginDataDto> {
		const userAccountEntity = await this.validateUser(userLoginDto, ip, isSSO);

		const token = await this.createToken({
			userId: userAccountEntity.userId,
		});

		await this.userService.updateLastLoginInfo(userAccountEntity.userId, ip);
		await this.userService.updateRefreshToken(
			userAccountEntity.userId,
			token.refreshToken,
		);

		this.eventBus.publish(
			new UserLoggedInEvent(
				userAccountEntity.userId,
				ip,
				isSSO ? 'sso' : 'password',
			),
		);

		return getLoginData(new LoginUserDto(userAccountEntity), token, ip);
	}

	async register(
		userRegisterDto: UserRegisterDto,
		ip: string,
	): Promise<RegisterDataDto> {
		const { identifier, verificationToken } = userRegisterDto;

		let payload: { contact: string; type: string };
		try {
			payload = await this.jwtService.verifyAsync(verificationToken);
		} catch {
			throw new InvalidVerificationTokenException('Invalid or expired verification token');
		}

		if (payload.contact !== identifier) {
			throw new UserNotFoundException(
				'Verification token does not match the provided contact information',
			);
		}
		if (payload.type !== TokenType.VERIFICATION_TOKEN) {
			throw new InvalidVerificationTokenException('Invalid verification token');
		}

		const { existingUser, contactType } = await this.userService.findExistingUser(
			identifier,
		);

		if (existingUser) {
			throw new AccountAlreadyExistsException('User with this email/phone already exists');
		}

		const createdUser = await this.userService.createUser(userRegisterDto, ip);
		const tokens = await this.createToken({ userId: createdUser.userId });

		this.userService.updateLastLoginInfo(createdUser.userId, ip);
		this.userService.updateRefreshToken(createdUser.userId, tokens.refreshToken);

		const registrationMethod = `${contactType}_otp`;
		this.eventBus.publish(
			new UserRegisteredEvent(createdUser.userId, ip, registrationMethod),
		);

		return {
			user: createdUser.toDto(),
			tokens,
			metadata: {
				registeredAt: new Date().toISOString(),
				registrationMethod,
				ipAddress: ip,
			},
		};
	}

	async refreshToken(
		refreshTokenDto: RefreshTokenDto,
	): Promise<RefreshTokenDataDto> {
		const { refreshToken } = refreshTokenDto;
		let decoded: any;

		try {
			decoded = this.jwtService.verify(refreshToken);
		} catch (error: unknown) {
			this.eventBus.publish(
				new RefreshTokenFailedEvent(undefined, 'invalid_token', error as Error),
			);
			throw new InvalidRefreshTokenException('Invalid refresh token');
		}

		const user = await this.userService.findOne({
			userId: decoded.userId,
		});

		if (!user) {
			this.eventBus.publish(
				new RefreshTokenFailedEvent(decoded.userId, 'user_not_found'),
			);
			throw new UserNotFoundException();
		}

		const isBlacklisted = await this.redisService.exists(
			this.redisKeyManager.auth.blacklistToken(refreshToken),
		);

		if (isBlacklisted) {
			this.eventBus.publish(
				new RefreshTokenFailedEvent(user.userId, 'blacklisted'),
			);
			throw new TokenBlacklistedException('Token has been blacklisted');
		}

		if (user.refreshToken !== refreshToken) {
			this.eventBus.publish(
				new RefreshTokenFailedEvent(user.userId, 'mismatch'),
			);
			throw new InvalidRefreshTokenException('Invalid refresh token');
		}

		const token = await this.createToken({
			userId: user.userId,
		});

		await this.userService.updateRefreshToken(user.userId, token.refreshToken);

		this.eventBus.publish(new RefreshTokenSuccessEvent(user.userId));

		return {
			tokens: token,
			metadata: {
				refreshedAt: new Date().toISOString(),
				expiresAt: new Date(
					Date.now() +
            this.configService.authConfig.jwtRefreshTokenExpirationTime * 1000,
				).toISOString(),
			},
		};
	}

	async logout(user: UserAccountEntity, request: Request): Promise<LogoutDataDto> {
		const authHeader = request.headers.authorization;

		if (authHeader?.startsWith('Bearer ')) {
			const token = authHeader.substring(7);
			await this.blacklistToken(token);
		}

		await this.userService.updateRefreshToken(user.userId, null);
		await this.clearUserSession(user.userId);

		this.eventBus.publish(new UserLoggedOutEvent(user.userId, request.ip || ''));

		console.info(`User ${user.username} logged out`);

		return {
			metadata: {
				logoutAt: new Date().toISOString(),
			},
		};
	}

	async verifyContact<T extends { contact: string }>(
		dto: T,
		ip: string,
	): Promise<ContactVerifyDataDto> {
		const { contact } = dto;

		const { existingUser, contactType } = await this.userService.findExistingUser(
			contact,
		);
		if (existingUser) {
			if (existingUser.status === UserAccountStatus.ACTIVE) {
				// Log contact verification for existing active user
				this.eventBus.publish(
					new VerifyContactEvent(
						existingUser.userId, 
						contact, 
						'existing_user',
					),
				);
				
				return {
					status: 'LOGIN_REQUIRED',
					contactType,
					message: 'User exists. Please login with your password.',
					details: {
						userExists: true,
						accountStatus: existingUser.status,
					},
				};
			}

			throw new UserInactiveException(
				'Account exists but is not active. Please contact support.',
			);
		}

		// Log contact verification for new user
		this.eventBus.publish(
			new VerifyContactEvent(
				0, 
				contact, 
				'new_user_registration',
			),
		);

		await this.otpService.sendOtp(contact, ip);

		return {
			status: 'OTP_SENT',
			contactType,
			message: `OTP sent to your ${contactType} for registration.`,
			details: {
				userExists: false,
			},
		};
	}
	async requestPasswordReset(
		resetPasswordRequestDto: ResetPasswordRequestDto,
		ip: string,
	): Promise<ResetPasswordRequestDataDto> {
		const { emailOrPhone } = resetPasswordRequestDto;
		const { existingUser } = await this.userService.findExistingUser(emailOrPhone);
		if (!existingUser || existingUser.status !== UserAccountStatus.ACTIVE) {
			return {
				target: emailOrPhone,
				expiresIn: 300, // 5 minutes
			};
		}

		const otpResult = await this.otpService.sendOtp(emailOrPhone, ip);

		return {
			target: otpResult.target,
			expiresIn: otpResult.expiresIn,
		};
	}

	async changePassword(
		user: UserAccountEntity,
		changePasswordDto: ChangePasswordDto,
		ip: string,
	): Promise<void> {
		const { currentPassword, newPassword } = changePasswordDto;
		const isPasswordValid = await validateHash(
			currentPassword,
			user.passwordHash,
		);

		if (!isPasswordValid) {
			throw new IncorrectPasswordException('Current password is incorrect');
		}

		await this.userService.updatePassword(user.userId, newPassword);
		await this.clearUserSession(user.userId);

		this.eventBus.publish(new PasswordChangedEvent(user.userId, ip));
	}

	async resetPassword(
		resetPasswordDto: ResetPasswordDto,
		ip: string,
	): Promise<ResetPasswordDataDto> {
		const { emailOrPhone, verificationToken, newPassword } = resetPasswordDto;
		const isEmailType = isEmail(emailOrPhone);

		let payload: { contact: string; type: string };
		try {
			payload = await this.jwtService.verifyAsync(verificationToken);
		} catch {
			throw new InvalidVerificationTokenException('Invalid or expired verification token');
		}

		if (payload.type !== TokenType.VERIFICATION_TOKEN) {
			throw new InvalidVerificationTokenException('Invalid verification token');
		}
		if (payload.contact !== emailOrPhone) {
			throw new UserNotFoundException(
				'Verification token does not match the provided contact information',
			);
		}
		const user = await this.userService.findOne({
			[isEmailType ? 'email' : 'phone']: emailOrPhone,
		});

		if (!user) {
			throw new UserNotFoundException('User not found');
		}

		await this.userService.updatePassword(user.userId, newPassword);

		this.eventBus.publish(new PasswordResetEvent(user.userId, ip));

		return {
			metadata: {
				resetAt: new Date().toISOString(),
				method: 'reset_password',
			},
		};
	}

	// --- Private and Utility Methods ---

	private async getAuthConfig<T>(key: string, defaultValue: T): Promise<T> {
		return this.configHelper.getConfigWithFallback(key, defaultValue, 'auth');
	}

	async invalidateUserCache(userId: number): Promise<void> {
		const cacheKey = this.redisKeyManager.auth.userToken(userId);
		try {
			await this.redisService.del(cacheKey);
		} catch (_error) {
			console.error(_error);
		}
	}

	async createToken(data: { userId: number }): Promise<TokenPayloadDto> {
		return new TokenPayloadDto(
			await createToken(
				this.jwtService,
				data.userId,
				this.configService.authConfig.jwtExpirationTime,
				this.configService.authConfig.jwtRefreshTokenExpirationTime,
			),
		);
	}

	async createSsoToken(data: { userId: number }): Promise<string> {
		return this.jwtService.signAsync(
			{ userId: data.userId, type: TokenType.SSO_TOKEN, role: RoleType.SSO_USER },
			{ expiresIn: '60s' },
		);
	}

	private async validateUserCommon(
		username: string,
		password: string | undefined,
		ip: string,
		isSSO = false,
		isSdk = false,
	): Promise<{ 'OTP_SENT': boolean, user: UserAccountEntity }> {
		await this.checkLoginRateLimit(username, ip);

		const contactType = getContactType(username);

		const user = await this.userService.findOne({
			[contactType]: username,
		});

		if (!user) {
			await this.recordFailedLogin(username, ip);
			throw new UserNotFoundException('User not found');
		}

		if (user.status === UserAccountStatus.INACTIVE) {
			if (isSdk) {
				await this.otpService.sendOtp(username, ip);
				return { 'OTP_SENT': true, user };
			}
			await this.recordFailedLogin(username, ip);
			throw new AccountNotActiveException('Account is not active');
		}

		if (isSSO) {
			await this.clearFailedLogins(username, ip);
			return { 'OTP_SENT': false, user };
		}

		if (!password) {
			await this.recordFailedLogin(username, ip);
			throw new PasswordRequiredException('Password is required');
		}

		const isPasswordValid = await validateHash(
			password,
			user.passwordHash,
		);

		if (!isPasswordValid) {
			await this.recordFailedLogin(username, ip);
			throw new InvalidPasswordException('Invalid password');
		}

		await this.clearFailedLogins(username, ip);

		return { 'OTP_SENT': false, user };
	}

	private async validateUser(
		userLoginDto: UserLoginDto,
		ip: string,
		isSSO = false,
	): Promise<UserAccountEntity> {
		const { user } = await this.validateUserCommon(
			userLoginDto.username,
			userLoginDto.password,
			ip,
			isSSO,
		);
		return user;
	}

	private async checkLoginRateLimit(username: string, ip: string): Promise<void> {
		const [maxAttempts, ipMaxAttempts, userAttempts, ipAttempts] = await Promise.all([
			this.getAuthConfig('max_login_attempts', 5),
			this.getAuthConfig('max_ip_login_attempts', 20),
			this.redisService.get<number>(
				this.redisKeyManager.auth.loginAttempts(username),
			),
			this.redisService.get<number>(this.redisKeyManager.auth.loginAttemptsIp(ip)),
		]);

		if (userAttempts && userAttempts >= maxAttempts) {
			throw new TooManyLoginAttemptsException('Too many login attempts for this account.');
		}
		if (ipAttempts && ipAttempts >= ipMaxAttempts) {
			throw new TooManyLoginAttemptsException('Too many login attempts from this IP.');
		}
	}

	private async recordFailedLogin(username: string, ip: string): Promise<void> {
		const ttl = await this.getAuthConfig('login_attempt_ttl', 900);
		const pipeline = this.redisService.createPipeline();
		pipeline.incr(this.redisKeyManager.auth.loginAttempts(username));
		pipeline.expire(this.redisKeyManager.auth.loginAttempts(username), ttl);
		pipeline.incr(this.redisKeyManager.auth.loginAttemptsIp(ip));
		pipeline.expire(this.redisKeyManager.auth.loginAttemptsIp(ip), ttl);
		await pipeline.exec();
	}

	private async clearFailedLogins(username: string, ip: string): Promise<void> {
		const pipeline = this.redisService.createPipeline();
		pipeline.del(this.redisKeyManager.auth.loginAttempts(username));
		pipeline.del(this.redisKeyManager.auth.loginAttemptsIp(ip));
		await pipeline.exec();
	}

	async getUserFromToken(token: string): Promise<UserAccountEntity> {
		const payload = await this.jwtService.verifyAsync(token, {
			secret: this.configService.authConfig.publicKey,
		});

		if (payload.type !== TokenType.ACCESS_TOKEN) {
			throw new UserNotFoundException();
		}

		const user = await this.userService.findOne({ userId: payload.userId });

		if (!user) {
			throw new UserNotFoundException();
		}

		return user;
	}

	async getUserFromRefreshToken(token: string): Promise<UserAccountEntity> {
		const payload = await this.jwtService.verifyAsync(token, {
			secret: this.configService.authConfig.publicKey,
		});

		if (payload.type !== TokenType.REFRESH_TOKEN) {
			throw new UserNotFoundException();
		}

		const user = await this.userService.findOne({ userId: payload.userId });

		if (!user || user.refreshToken !== token) {
			throw new InvalidRefreshTokenException('Invalid refresh token');
		}

		return user;
	}

	async loginWithOAuth(
		socialInfo: SocialInfoDto,
		ip: string,
	): Promise<{ ssoToken?: string | null; userAccount: UserAccountEntity }> {
		const { email, socialUid, provider } = socialInfo;
		let matchedUser: UserAccountEntity | null = null;

		if (socialUid) {
			matchedUser = await this.userService.findOne({
				socialUid,
				accountType: provider,
			});
		}

		if (!matchedUser && email) {
			const userByEmail = await this.userService.findOne({ email });
			if (userByEmail) {
				if (userByEmail.accountType === UserAccountType.QUICKPLAY) {
					throw new QuickplayLinkException('Cannot link Quickplay account.');
				}
				if (userByEmail.socialUid) {
					throw new AccountAlreadyExistsException(
						'Account already linked to another social provider.',
					);
				}
				matchedUser = await this.userService.linkUserSSO(userByEmail, socialInfo);
			}
		}

		if (!matchedUser) {
			matchedUser = await this.userService.createUserSSO(socialInfo, ip);
			this.eventBus.publish(
				new UserRegisteredEvent(matchedUser.userId, ip, `social_${provider}`),
			);
		}

		if (matchedUser.status !== UserAccountStatus.ACTIVE) {
			return { ssoToken: null, userAccount: matchedUser };
		}

		await this.userService.updateLastLoginInfo(matchedUser.userId, ip, socialInfo.accessToken);

		this.eventBus.publish(
			new UserLoggedInEvent(matchedUser.userId, ip, 'social'),
		);

		const ssoToken = await this.createSsoToken({ userId: matchedUser.userId });
		return { ssoToken, userAccount: matchedUser };
	}

	async blacklistToken(token: string): Promise<void> {
		try {
			const payload = await this.jwtService.verifyAsync(token, {
				secret: this.configService.authConfig.publicKey,
			});
			const ttl = payload.exp - Math.floor(Date.now() / 1000);
			if (ttl > 0) {
				await this.redisService.set(
					this.redisKeyManager.auth.blacklistToken(token),
					'true',
					ttl,
				);
			}
		} catch {
			// Invalid token, no need to blacklist
		}
	}

	async isTokenBlacklisted(token: string): Promise<boolean> {
		const key = this.redisKeyManager.auth.blacklistToken(token);
		return (await this.redisService.get<string>(key)) === 'true';
	}

	async cacheUserSession(userId: number, sessionData: any): Promise<void> {
		const key = this.redisKeyManager.user.session(userId.toString());
		await this.redisService.set(key, sessionData, 3600);
	}

	async getUserSession(userId: number): Promise<any | null> {
		const key = this.redisKeyManager.user.session(userId.toString());
		return this.redisService.get(key);
	}

	async clearUserSession(userId: number): Promise<void> {
		const key = this.redisKeyManager.user.session(userId.toString());
		await this.redisService.del(key);
	}
}
