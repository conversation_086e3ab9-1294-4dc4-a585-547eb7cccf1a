import { UserAccountDto } from '@modules/user/dtos/user-account.dto';
import { ApiProperty } from '@nestjs/swagger';

import { LoginUserDto } from './login-user.dto';
import { TokenPayloadDto } from './token-payload.dto';

// Data payload for contact verification response
export class ContactVerifyDataDto {
	@ApiProperty({
		description: 'Operation status',
		enum: ['LOGIN_REQUIRED', 'OTP_SENT', 'ALREADY_VERIFIED'],
		example: 'LOGIN_REQUIRED',
	})
	status!: 'LOGIN_REQUIRED' | 'OTP_SENT' | 'ALREADY_VERIFIED';

	@ApiProperty({
		description: 'Detected contact type',
		enum: ['email', 'phone', 'username'],
		example: 'email',
	})
	contactType!: 'email' | 'phone' | 'username';

	@ApiProperty({
		description: 'Human-readable message',
		example: 'User exists. Please login with your password.',
	})
	message?: string;

	@ApiProperty({
		description: 'Additional data for client handling',
		type: 'object',
		properties: {
			userExists: {
				type: 'boolean',
				description: 'Whether user account exists',
				example: true,
			},
			accountStatus: {
				type: 'string',
				description: 'Account status if user exists',
				example: 'ACTIVE',
			},
		},
	})
	details?: {
		userExists: boolean;
		accountStatus?: string;
	};
}

// Data payload for registration response
export class RegisterDataDto {
	@ApiProperty({
		description: 'User account information',
		type: UserAccountDto,
	})
	user!: UserAccountDto;

	@ApiProperty({
		description: 'Authentication tokens',
		type: TokenPayloadDto,
	})
	tokens!: TokenPayloadDto;

	@ApiProperty({
		description: 'Registration metadata',
		type: 'object',
		properties: {
			registeredAt: {
				type: 'string',
				format: 'date-time',
				description: 'Registration timestamp',
				example: '2025-08-16T10:30:00Z',
			},
			registrationMethod: {
				type: 'string',
				description: 'How user registered',
				example: 'email_otp',
			},
			ipAddress: {
				type: 'string',
				description: 'Registration IP address',
				example: '***********',
			},
		},
	})
	metadata!: {
		registeredAt: string;
		registrationMethod: string;
		ipAddress: string;
	};
}

// Data payload for login response
export class LoginDataDto {
	@ApiProperty({
		description: 'User account information',
		type: LoginUserDto,
	})
	user!: LoginUserDto;

	@ApiProperty({
		description: 'Authentication tokens',
		type: TokenPayloadDto,
	})
	tokens!: TokenPayloadDto;

	@ApiProperty({
		description: 'Login metadata',
		type: 'object',
		properties: {
			loginAt: {
				type: 'string',
				format: 'date-time',
				description: 'Login timestamp',
				example: '2025-08-16T10:30:00Z',
			},
			ipAddress: {
				type: 'string',
				description: 'Login IP address',
				example: '***********',
			},
			userAgent: {
				type: 'string',
				description: 'User agent string',
				example: 'Mozilla/5.0...',
			},
		},
	})
	metadata!: {
		loginAt: string;
		ipAddress: string;
		userAgent?: string;
	};
}

// Data payload for password reset request
export class ResetPasswordRequestDataDto {
	@ApiProperty({
		description: 'Target contact where OTP was sent',
		example: '<EMAIL>',
	})
	target!: string;

	@ApiProperty({
		description: 'OTP expiration time in seconds',
		example: 300,
	})
	expiresIn!: number;
}

// Data payload for password reset confirmation
export class ResetPasswordDataDto {
	@ApiProperty({
		description: 'Reset metadata',
		type: 'object',
		properties: {
			resetAt: {
				type: 'string',
				format: 'date-time',
				description: 'Password reset timestamp',
				example: '2025-08-16T10:30:00Z',
			},
			method: {
				type: 'string',
				description: 'Reset method used',
				example: 'otp_verification',
			},
		},
	})
	metadata!: {
		resetAt: string;
		method: string;
	};
}

// Data payload for token refresh response
export class RefreshTokenDataDto {
	@ApiProperty({
		description: 'New authentication tokens',
		type: TokenPayloadDto,
	})
	tokens!: TokenPayloadDto;

	@ApiProperty({
		description: 'Token metadata',
		type: 'object',
		properties: {
			refreshedAt: {
				type: 'string',
				format: 'date-time',
				description: 'Token refresh timestamp',
				example: '2025-08-16T10:30:00Z',
			},
			expiresAt: {
				type: 'string',
				format: 'date-time',
				description: 'New token expiration',
				example: '2025-08-16T11:30:00Z',
			},
		},
	})
	metadata!: {
		refreshedAt: string;
		expiresAt: string;
	};
}

// Data payload for logout response
export class LogoutDataDto {
	@ApiProperty({
		description: 'Logout metadata',
		type: 'object',
		properties: {
			logoutAt: {
				type: 'string',
				format: 'date-time',
				description: 'Logout timestamp',
				example: '2025-08-16T10:30:00Z',
			},
		},
	})
	metadata!: {
		logoutAt: string;
	};
}