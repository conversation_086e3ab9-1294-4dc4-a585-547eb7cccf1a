import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, Matches, MinLength } from 'class-validator';

export class ChangePasswordDto {
	@ApiProperty({
		example: 'CurrentPassword123!',
		description: 'Current password for verification',
	})
	@IsString()
	@IsNotEmpty()
	currentPassword!: string;

	@ApiProperty({
		example: 'NewPassword123!',
		description:
			'New password (min 8 characters, must contain uppercase, lowercase, number and special character)',
	})
	@IsString()
	@IsNotEmpty()
	@MinLength(8)
	@Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, {
		message:
			'Password must contain at least one uppercase letter, one lowercase letter, one number and one special character',
	})
	newPassword!: string;
}