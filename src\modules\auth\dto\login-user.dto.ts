import { maskEmail, maskId } from '@common/utils';
import {
	Em<PERSON>FieldOptional,
	NumberField,
	StringFieldOptional,
	VietnamesePhoneFieldOptional,
} from '@decorators/field.decorators';
import type { UserAccountEntity } from '@modules/user/user-account.entity';

export class LoginUserDto {
	@NumberField()
	userId!: number;

	@StringFieldOptional({ nullable: true })
	username!: string | null;

	@EmailFieldOptional({ nullable: true })
	email?: string | null;

	@VietnamesePhoneFieldOptional({ nullable: true })
	phone?: string | null;

	constructor(userAccount: UserAccountEntity) {
		this.userId = userAccount.userId;
		this.username = userAccount.username ?? null;
		this.email = maskEmail(userAccount.email ?? '', 1, 3);
		this.phone = maskId(userAccount.phone ?? '', 0, 4);
	}
}
