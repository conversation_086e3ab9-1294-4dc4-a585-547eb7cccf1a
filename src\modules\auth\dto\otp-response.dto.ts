import { ApiProperty } from '@nestjs/swagger';

export class SendOtpDataDto {
	@ApiProperty({
		description: 'Detected contact type',
		example: 'OTP_SENT',
	})
	status!: string;
	
	@ApiProperty({
		description: 'Target contact (email or phone) where <PERSON><PERSON> was sent',
		example: '<EMAIL> or 84905060708',
	})
	contact!: string;
}

export class VerifyOtpDataDto {
	@ApiProperty({
		description: 'Target contact that was verified',
		example: '<EMAIL> or 84905060708',
	})
	target!: string;

	@ApiProperty({
		description: 'Verification token for next steps (if applicable)',
		example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
		required: false,
	})
	verificationToken?: string;

	@ApiProperty({
		description: 'Verification status',
		example: true,
		required: false,
	})
	verified?: boolean;

	@ApiProperty({
		format: 'date-time',
		description: 'Verification timestamp',
		example: '2025-08-16T10:30:00Z',
		required: false,
	})
	verifiedAt?: string;
}