import { EmailOrPhoneField } from '@decorators/field.decorators';
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, Matches, MinLength } from 'class-validator';

export class ResetPasswordRequestDto {
	@EmailOrPhoneField({
		example: '<EMAIL> or 84905060708',
		description:
			'Email address or Vietnamese phone number to send reset password OTP',
	})
	emailOrPhone!: string;
}

export class ResetPasswordDto {
	@EmailOrPhoneField({
		example: '<EMAIL> or 84905060708',
		description: 'Email or phone number that received the OTP',
	})
	emailOrPhone!: string;

	@ApiProperty({
		example: 'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...',
		description: 'Verification token received from /otp/verify endpoint',
	})
	@IsString()
	@IsNotEmpty()
	verificationToken!: string;

	@ApiProperty({
		example: 'NewPassword123!',
		description:
			'New password (min 8 characters, must contain uppercase, lowercase, number and special character)',
	})
	@IsString()
	@IsNotEmpty()
	@MinLength(8)
	@Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, {
		message:
			'Password must contain at least one uppercase letter, one lowercase letter, one number and one special character',
	})
	newPassword!: string;
}
