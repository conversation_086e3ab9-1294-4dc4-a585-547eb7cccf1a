import { UserAccountType } from '@constants/user';
import {
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON>umField,
	StringField,
} from '@decorators/field.decorators';
import { Exclude } from 'class-transformer';

export class SocialInfoDto {
	@StringField()
	socialUid!: string;

	@StringField()
	name?: string | null;

	@EmailField()
	email?: string | null;

	@StringField()
	avatarUrl?: string | null;

	@EnumField(() => UserAccountType)
	provider!: UserAccountType;

	@StringField()
	@Exclude()
	accessToken?: string | null;

	@StringField()
	@Exclude()
	refreshToken?: string | null;

	constructor(partial: Partial<SocialInfoDto>) {
		Object.assign(this, partial);
	}
}
