import { NumberField, StringField } from '@decorators/field.decorators';
import { Exclude } from 'class-transformer';

export class TokenPayloadDto {
	@Exclude()
	@NumberField()
	userId: number;

	@NumberField()
	expiresIn: number;

	@StringField()
	token: string;

	@StringField()
	refreshToken: string;

	constructor(data: {
		userId: number;
		expiresIn: number;
		token: string;
		refreshToken: string;
	}) {
		this.userId = data.userId;
		this.expiresIn = data.expiresIn;
		this.token = data.token;
		this.refreshToken = data.refreshToken;
	}
}
