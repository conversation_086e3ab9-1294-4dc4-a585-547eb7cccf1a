import type { SsoType } from '@constants/sso-type';
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, MinLength } from 'class-validator';

export class UserLoginDto {
	@ApiProperty({
		example: '<EMAIL>',
		description: 'Username, email, or phone number',
	})
	@IsString()
	@IsNotEmpty()
	readonly username!: string;

	@ApiProperty({
		example: 'Password123!',
		description: 'User password',
	})
	@IsString()
	@IsNotEmpty()
	@MinLength(1)
	readonly password!: string;
}

export class UserLoginSdkDto extends UserLoginDto {
	@ApiProperty({
		example: 'gameKey',
		description: 'Game Id',
	})
	@IsString()
	@IsNotEmpty()
	readonly gameKey!: string;

	@ApiProperty({
		example: 'qp_123456',
		description: 'optional quickplay Id',
	})
	@IsString()
	readonly quickplayId?: string;
}

export class UserLoginSdkSsoDto {
	@ApiProperty({
		example: 'vl1hn',
		description: 'Game key identifier',
	})
	@IsString()
	@IsNotEmpty()
	readonly gameKey!: string;
	
	@ApiProperty({
		example: 'eyJhbGciOiJSUzI1NiIsImtpZCI6Ijk...',
		description: 'Access token of social account',
	})
	@IsString()
	@IsNotEmpty()
	readonly accessToken!: string;
	
	@ApiProperty({
		example: 'google',
		description: 'SSO type: google / facebook',
	})
	@IsString()
	@IsNotEmpty()
	readonly ssoType!: SsoType;
}
