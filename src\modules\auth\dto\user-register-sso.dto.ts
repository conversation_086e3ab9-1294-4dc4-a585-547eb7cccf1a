import { UserAccountStatus, UserAccountType } from '@constants/user';
import {
	DateFieldOptional,
	EnumField,
	StringFieldOptional,
} from '@decorators/field.decorators';
import { Exclude } from 'class-transformer';

export class UserRegisterSsoDto {
	@StringFieldOptional()
	username?: string | null;

	@StringFieldOptional()
	@Exclude()
	email?: string | null;

	@EnumField(() => UserAccountStatus)
	@Exclude()
	status!: UserAccountStatus;

	@EnumField(() => UserAccountType)
	@Exclude()
	accountType!: UserAccountType;

	@StringFieldOptional()
	@Exclude()
	socialUid?: string | null;

	@DateFieldOptional()
	@Exclude()
	linkedAt?: Date | null;

	@StringFieldOptional()
	@Exclude()
	socialAccessToken?: string | null;

	@StringFieldOptional()
	@Exclude()
	socialRefreshToken?: string | null;

	@StringFieldOptional()
	@Exclude()
	createdAtIp?: string | null;

	constructor(partial: Partial<UserRegisterSsoDto>) {
		Object.assign(this, partial);

		this.accountType = partial.accountType! || UserAccountType;
		this.status = UserAccountStatus.ACTIVE;
	}
}
