import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, Matches, MinLength } from 'class-validator';

export class UserRegisterDto {
	@ApiProperty({
		example: '<EMAIL>',
		description: 'Email or phone number (verified via OTP)',
	})
	@IsString()
	@IsNotEmpty()
	readonly identifier?: string;

	@ApiProperty({
		example: 'Password123!',
		description:
			'Password (min 8 characters, must contain uppercase, lowercase, number and special character)',
	})
	@IsString()
	@IsNotEmpty()
	@MinLength(8)
	@Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, {
		message:
			'Password must contain at least one uppercase letter, one lowercase letter, one number and one special character',
	})
	password!: string;

	@ApiProperty({
		example: 'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...',
		description: 'Verification token from OTP verification',
	})
	@IsString()
	@IsNotEmpty()
	verificationToken!: string;
}
