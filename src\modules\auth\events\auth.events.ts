export class UserLoggedInEvent {
	constructor(
		public readonly userId: number,
		public readonly ip: string,
		public readonly loginMethod: 'password' | 'sso' | 'social',
	) {}
}

export class UserRegisteredEvent {
	constructor(
		public readonly userId: number,
		public readonly ip: string,
		public readonly registrationMethod: string,
	) {}
}

export class UserLoggedOutEvent {
	constructor(
		public readonly userId: number,
		public readonly ip: string,
	) {}
}

export class VerifyContactEvent {
	constructor(
		public readonly userId: number,
		public readonly contact: string,
		public readonly status: string,
	) {}
}

export class PasswordChangedEvent {
	constructor(
		public readonly userId: number,
		public readonly ip: string,
	) {}
}

export class PasswordResetEvent {
	constructor(
		public readonly userId: number,
		public readonly ip: string,
	) {}
}

export class RefreshTokenSuccessEvent {
	constructor(public readonly userId: number) {}
}

export class RefreshTokenFailedEvent {
	constructor(
		public readonly userId: number | undefined,
		public readonly reason: string,
		public readonly error?: Error,
	) {}
}