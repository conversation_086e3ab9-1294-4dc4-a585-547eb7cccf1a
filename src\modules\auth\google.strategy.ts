import { UserAccountType } from '@constants/user';
import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ApiConfigService } from '@shared/services/api-config.service.ts';
import type { VerifyCallback } from 'passport-google-oauth20';
import { Strategy } from 'passport-google-oauth20';

import { SocialInfoDto } from './dto/social-info.dto.ts';

@Injectable()
export class GoogleStrategy extends PassportStrategy(Strategy, 'google') {
	constructor(configService: ApiConfigService) {
		super({
			...configService.googleCert,
			callbackURL: `${configService.backendUrl}/auth/oauth2/google/callback`,
			scope: ['email', 'profile'],
		});
	}

	async validate(
		accessToken: string,
		refreshToken: string,
		profile: any,
		done: VerifyCallback,
	): Promise<any> {
		const { name, emails, photos } = profile;
		const socialInfo = new SocialInfoDto({
			socialUid: profile.id,
			name: profile.displayName || `${name.givenName} ${name.familyName}`,
			email: emails[0].value,
			avatarUrl: photos[0].value,
			provider: UserAccountType.GOOGLE,
			accessToken,
			refreshToken,
		});
		done(null, socialInfo);
	}
}
