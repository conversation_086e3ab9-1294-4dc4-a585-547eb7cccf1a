# Health Checker Module

## 📋 Overview

The Health Checker module provides basic health monitoring for the FS Player Service. It currently monitors database connectivity to ensure system reliability.

## 🔧 Features

### System Health Monitoring
- **Database Health** - PostgreSQL connection and query testing with timeout
- **Simple Status Check** - Basic health endpoint for load balancers
- **Timeout Protection** - 1.5 second timeout to prevent hanging

## 🚀 API Endpoints (Real APIs Only)

### Health Check
```bash
GET    /health               # Database health check (public endpoint)
```

## 📊 Health Check Response

### Basic Health Check
```bash
GET /health
```

**Response (Healthy):**
```json
{
  "status": "ok",
  "info": {
    "database": {
      "status": "up",
      "responseTime": 45
    }
  },
  "error": {},
  "details": {
    "database": {
      "status": "up",
      "responseTime": 45
    }
  }
}
```

**Response (Unhealthy):**
```json
{
  "status": "error",
  "info": {},
  "error": {
    "database": {
      "status": "down",
      "message": "Connection timeout",
      "responseTime": 1500
    }
  },
  "details": {
    "database": {
      "status": "down",
      "message": "Connection timeout",
      "responseTime": 1500
    }
  }
}
```

### Detailed Health Report
```bash
GET /health/detailed
```

**Response:**
```json
{
  "status": "ok",
  "timestamp": "2023-01-01T12:00:00.000Z",
  "uptime": 86400,
  "version": "1.0.0",
  "environment": "production",
  "checks": {
    "database": {
      "status": "up",
      "responseTime": 45,
      "connections": {
        "active": 5,
        "idle": 10,
        "total": 15
      }
    },
    "redis": {
      "status": "up",
      "responseTime": 12,
      "memory": {
        "used": "256MB",
        "peak": "512MB"
      }
    },
    "memory": {
      "status": "ok",
      "usage": {
        "used": "512MB",
        "total": "2GB",
        "percentage": 25
      }
    },
    "disk": {
      "status": "ok",
      "usage": {
        "used": "10GB",
        "total": "100GB",
        "percentage": 10
      }
    }
  }
}
```

## 🔧 Health Check Configuration

### Database Health Check
```typescript
// Check database connectivity with timeout
async checkDatabase(): Promise<HealthIndicatorResult> {
  return this.ormIndicator.pingCheck('database', {
    timeout: 1500,
    connection: 'default'
  });
}
```

### Redis Health Check
```typescript
// Check Redis connectivity and performance
async checkRedis(): Promise<HealthIndicatorResult> {
  return this.redisIndicator.checkHealth('redis', {
    type: 'redis',
    url: process.env.REDIS_URL,
    timeout: 1000
  });
}
```

### Memory Health Check
```typescript
// Check memory usage
async checkMemory(): Promise<HealthIndicatorResult> {
  return this.memoryIndicator.checkHeap('memory_heap', 150 * 1024 * 1024);
}
```

### Disk Health Check
```typescript
// Check disk space
async checkDisk(): Promise<HealthIndicatorResult> {
  return this.diskIndicator.checkStorage('storage', {
    path: '/',
    thresholdPercent: 0.9
  });
}
```

## 📈 Monitoring Integration

### Prometheus Metrics
```bash
GET /health/metrics
```

**Response:**
```
# HELP health_check_status Health check status (1 = healthy, 0 = unhealthy)
# TYPE health_check_status gauge
health_check_status{service="database"} 1
health_check_status{service="redis"} 1
health_check_status{service="memory"} 1

# HELP health_check_response_time Health check response time in milliseconds
# TYPE health_check_response_time gauge
health_check_response_time{service="database"} 45
health_check_response_time{service="redis"} 12

# HELP application_uptime Application uptime in seconds
# TYPE application_uptime counter
application_uptime 86400
```

### Kubernetes Integration
```yaml
# Kubernetes deployment with health checks
apiVersion: apps/v1
kind: Deployment
spec:
  template:
    spec:
      containers:
      - name: fs-player-service
        livenessProbe:
          httpGet:
            path: /health/liveness
            port: 4001
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health/readiness
            port: 4001
          initialDelaySeconds: 5
          periodSeconds: 5
```

## 🚨 Health Check Thresholds

### Response Time Thresholds
```typescript
const healthThresholds = {
  database: {
    timeout: 1500,      // 1.5 seconds
    warning: 1000,      // 1 second
    critical: 1500      // 1.5 seconds
  },
  redis: {
    timeout: 1000,      // 1 second
    warning: 500,       // 500ms
    critical: 1000      // 1 second
  },
  externalApi: {
    timeout: 5000,      // 5 seconds
    warning: 3000,      // 3 seconds
    critical: 5000      // 5 seconds
  }
};
```

### Resource Thresholds
```typescript
const resourceThresholds = {
  memory: {
    warning: 0.8,       // 80% usage
    critical: 0.9       // 90% usage
  },
  disk: {
    warning: 0.8,       // 80% usage
    critical: 0.9       // 90% usage
  },
  cpu: {
    warning: 0.7,       // 70% usage
    critical: 0.9       // 90% usage
  }
};
```

## 🔧 Custom Health Indicators

### External Service Health Check
```typescript
@Injectable()
export class ExternalServiceHealthIndicator extends HealthIndicator {
  async checkExternalService(key: string): Promise<HealthIndicatorResult> {
    try {
      const response = await this.httpService.get('/external-api/health').toPromise();
      const isHealthy = response.status === 200;
      
      return this.getStatus(key, isHealthy, {
        responseTime: response.headers['x-response-time'],
        status: response.data.status
      });
    } catch (error) {
      return this.getStatus(key, false, {
        message: error.message
      });
    }
  }
}
```

### Queue Health Check
```typescript
@Injectable()
export class QueueHealthIndicator extends HealthIndicator {
  async checkQueue(key: string): Promise<HealthIndicatorResult> {
    try {
      const queueStats = await this.queueService.getStats();
      const isHealthy = queueStats.waiting < 1000; // Less than 1000 waiting jobs
      
      return this.getStatus(key, isHealthy, {
        waiting: queueStats.waiting,
        active: queueStats.active,
        completed: queueStats.completed,
        failed: queueStats.failed
      });
    } catch (error) {
      return this.getStatus(key, false, {
        message: error.message
      });
    }
  }
}
```

## 📊 Health Check Scheduling

### Periodic Health Checks
```typescript
@Injectable()
export class HealthSchedulerService {
  @Cron('*/30 * * * * *') // Every 30 seconds
  async performHealthChecks() {
    const healthResult = await this.healthCheckService.check([
      () => this.database.checkDatabase(),
      () => this.redis.checkRedis(),
      () => this.memory.checkMemory()
    ]);
    
    // Store health status for monitoring
    await this.metricsService.recordHealthStatus(healthResult);
    
    // Alert if unhealthy
    if (healthResult.status === 'error') {
      await this.alertService.sendHealthAlert(healthResult);
    }
  }
}
```

## 🚨 Alerting Integration

### Health Alert Configuration
```typescript
const alertConfig = {
  channels: ['email', 'slack', 'webhook'],
  thresholds: {
    consecutive_failures: 3,    // Alert after 3 consecutive failures
    recovery_notification: true // Notify when service recovers
  },
  recipients: {
    email: ['<EMAIL>', '<EMAIL>'],
    slack: '#alerts',
    webhook: 'https://monitoring.example.com/webhook'
  }
};
```

### Alert Message Format
```json
{
  "severity": "critical",
  "service": "fs-player-service",
  "component": "database",
  "status": "down",
  "message": "Database connection timeout",
  "timestamp": "2023-01-01T12:00:00.000Z",
  "details": {
    "responseTime": 1500,
    "error": "Connection timeout after 1500ms"
  }
}
```

## 🔧 Configuration

### Environment Variables
```bash
HEALTH_CHECK_TIMEOUT=5000
HEALTH_CHECK_INTERVAL=30000
HEALTH_METRICS_ENABLED=true
HEALTH_ALERTS_ENABLED=true
```

### Module Configuration
```typescript
@Module({
  imports: [
    TerminusModule.forRoot({
      errorLogStyle: 'pretty',
      gracefulShutdownTimeoutMs: 1000
    })
  ],
  controllers: [HealthCheckerController],
  providers: [
    HealthCheckService,
    TypeOrmHealthIndicator,
    MemoryHealthIndicator,
    DiskHealthIndicator
  ]
})
export class HealthCheckerModule {}
```

## 📝 Notes

- Health checks run with configurable timeouts to prevent hanging
- Failed health checks are logged for debugging purposes
- Health status is cached briefly to prevent overwhelming checks
- Load balancers should use `/health` endpoint for routing decisions
- Kubernetes probes should use specific `/health/liveness` and `/health/readiness` endpoints
- Health metrics are exposed for monitoring system integration
- Critical health failures trigger immediate alerts
- Health check history is maintained for trend analysis

## 🔗 Related Modules

- [Sys-Config Module](../sys-config/README.md) - Health check configuration
- [Audit Log Module](../audit-log/README.md) - Health event logging
