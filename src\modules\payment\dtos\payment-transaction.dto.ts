import { AbstractDto } from '@common/dto/abstract.dto';
import { PaymentMethod, PaymentStatus } from '@constants/payment';
import {
	DateField,
	EnumField,
	NumberField,
	StringField,
} from '@decorators/field.decorators';

import type { PaymentTransactionEntity } from '../payment-transaction.entity';

export class PaymentTransactionDto extends AbstractDto {
	@NumberField()
	txId: number;

	@NumberField()
	userId: number;

	@NumberField()
	gameId: number;

	@StringField()
	orderId: string;

	@NumberField()
	amount: number;

	@StringField()
	currency: string;

	@EnumField(() => PaymentMethod)
	paymentMethod: PaymentMethod;

	@EnumField(() => PaymentStatus)
	status: PaymentStatus;

	@StringField()
	note: string;

	@DateField()
	createdAt: Date;

	@DateField()
	updatedAt: Date;

	constructor(paymentTransaction: PaymentTransactionEntity) {
		super(paymentTransaction);
		this.txId = paymentTransaction.txId;
		this.userId = paymentTransaction.userId;
		this.gameId = paymentTransaction.gameId;
		this.orderId = paymentTransaction.orderId;
		this.amount = paymentTransaction.amount;
		this.currency = paymentTransaction.currency;
		this.paymentMethod = paymentTransaction.paymentMethod as PaymentMethod;
		this.status = paymentTransaction.status as PaymentStatus;
		this.note = paymentTransaction.note;
		this.createdAt = paymentTransaction.createdAt;
		this.updatedAt = paymentTransaction.updatedAt;
	}
}
