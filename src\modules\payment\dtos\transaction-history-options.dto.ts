import { PageOptionsDto } from '@common/dto/page-options.dto';
import { PaymentStatus } from '@constants/payment';
import {
	DateFieldOptional,
	EnumFieldOptional,
} from '@decorators/field.decorators';

export class TransactionHistoryOptionsDto extends PageOptionsDto {
	@EnumFieldOptional(() => PaymentStatus)
	//   @Expose({ name: 'filter_status' })
	filterStatus?: PaymentStatus;

	@DateFieldOptional()
	//   @Expose({ name: 'date_from' })
	dateFrom?: Date;

	@DateFieldOptional()
	//   @Expose({ name: 'date_to' })
	dateTo?: Date;
}
