import { AbstractDto } from '@common/dto/abstract.dto';
import {
	DateField,
	StringField,
} from '@decorators/field.decorators';

import type { QuickplayEntity } from '../quickplay.entity';

export class QuickplayDto extends AbstractDto {
	@StringField()
	quickplayId?: string;

	@StringField()
	gameKey!: string;

	@StringField()
	platform!: string;

	@StringField()
	deviceId!: string;
	
	@StringField()
	ipAddress!: string;

	@DateField()
	declare createdAt: Date;

	constructor(quickplay: QuickplayEntity) {
		super(quickplay);
		this.quickplayId = quickplay.quickplayId;
		this.gameKey = quickplay.gameKey;
		this.platform = quickplay.platform;
		this.deviceId = quickplay.deviceId;
		this.ipAddress = quickplay.ipAddress;
		this.createdAt = quickplay.createdAt;
	}
}
