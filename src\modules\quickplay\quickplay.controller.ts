import { getIp } from '@common/utils';
import { ApiCommonResponse } from '@decorators/api-common-response.decorator';
import {
	Body,
	Controller,
	HttpCode,
	HttpStatus,
	Post,
	Req,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import type { Request } from 'express';

import { QuickplayDto } from './dtos/quickplay.dto';
import { QuickplayResponseDto } from './dtos/quickplay-response.dto';
import type { QuickplayService } from './quickplay.service';

@Controller('quickplay')
@ApiTags('Quickplay')
export class QuickplayController {
	constructor(private quickplayService: QuickplayService) { }

	@Post('login')
	@HttpCode(HttpStatus.OK)
	@ApiCommonResponse({
		type: QuickplayResponseDto,
		description: 'Create quickplay user',
	})
	async quickplay(
		@Body() quickplayDto: QuickplayDto,
		@Req() request: Request,
	): Promise<QuickplayResponseDto> {
		return this.quickplayService.loginQuickplay(
			quickplayDto,
			getIp(request),
		);
	}
}