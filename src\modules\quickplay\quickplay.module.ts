import { AuditLogModule } from '@modules/audit-log/audit-log.module';
import { UserProfileEntity } from '@modules/user/user-profile.entity';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SharedModule } from '@shared/shared.module';

import { QuickplayController } from './quickplay.controller';
import { QuickplayEntity } from './quickplay.entity';
import { QuickplayService } from './quickplay.service';

@Module({
	imports: [
		TypeOrmModule.forFeature([
			QuickplayEntity,
			UserProfileEntity,
		]),
		SharedModule,
		AuditLogModule,
	],
	controllers: [QuickplayController],
	exports: [QuickplayService],
	providers: [QuickplayService],
})
export class QuickplayModule {}
