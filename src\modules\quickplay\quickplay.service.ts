import { SocialCode, UserAccountType } from '@constants/user';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { GeneratorProvider } from 'providers/generator.provider.ts';
import { Repository } from 'typeorm';

import type { QuickplayDto } from './dtos/quickplay.dto.ts';
import { QuickplayResponseDto } from './dtos/quickplay-response.dto.ts';
import { QuickplayEntity } from './quickplay.entity.ts';

@Injectable()
export class QuickplayService {
	constructor(
		@InjectRepository(QuickplayEntity)
		private quickplayRepository: Repository<QuickplayEntity>,
	) {}

	private async createQuickplay(
		quickplayDto: QuickplayDto,
		ip: string,
	): Promise<QuickplayResponseDto> {
		const quickplayId = `${SocialCode.get(
			UserAccountType.QUICKPLAY,
		)}_${GeneratorProvider.uuidNoHyphens()}`;

		quickplayDto.quickplayId = quickplayId;
		quickplayDto.ipAddress = ip;
		await this.quickplayRepository.save(quickplayDto);

		return new QuickplayResponseDto({
			quickplayId,
		});
	}

	async loginQuickplay(
		quickplayDto: QuickplayDto,
		ip: string,
	): Promise<QuickplayResponseDto> {
		if (!quickplayDto.quickplayId) {
			return this.createQuickplay(quickplayDto, ip);
		}
		const quickplay = await this.quickplayRepository.findOneBy({
			quickplayId: quickplayDto.quickplayId,
			gameKey: quickplayDto.gameKey,
			platform: quickplayDto.platform,
			deviceId: quickplayDto.deviceId,
		});
		if (!quickplay) {
			return this.createQuickplay(quickplayDto, ip);
		}
		return new QuickplayResponseDto({
			quickplayId: quickplay.quickplayId,
		});
	}

	async checkQuickplayExists(quickplayId: string, gameKey: string): Promise<boolean> {
		const quickplay = await this.quickplayRepository.findOneBy({
			quickplayId,
			gameKey,
		});
		return !!quickplay;
	}
}
