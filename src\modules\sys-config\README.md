# System Configuration Module

Enterprise-grade system configuration management with Redis caching, type safety, and comprehensive API documentation.

## 🎯 Overview

The SysConfig module provides a centralized, high-performance configuration management system for the FS Player Service. It supports dynamic configuration updates, type-safe value handling, Redis caching, and comprehensive security features.

## ✨ Features

### Core Features
- **🔑 Key-based Primary Storage** - Uses configuration key as primary key for optimal performance
- **🎯 Type-Safe Values** - Automatic type conversion (string, number, boolean, json)
- **🔒 Secret Management** - Secure handling of sensitive configurations
- **📂 Category Organization** - Group related configurations for better management
- **⚡ Redis Caching** - High-performance caching with 1-hour TTL
- **📊 Cache Statistics** - Real-time performance monitoring
- **🛡️ Admin Security** - Role-based access control for sensitive operations

### Advanced Features
- **🔄 Bulk Operations** - Set multiple configurations in single request
- **📈 Performance Monitoring** - Cache hit/miss ratio tracking
- **🎛️ Cache Management** - Clear, preload, and statistics endpoints
- **🔍 Dynamic Filtering** - Filter by category, include/exclude secrets
- **📝 Comprehensive Documentation** - Full Swagger API documentation

## 🏗️ Architecture

### Database Schema
```sql
CREATE TABLE sys_config (
    key VARCHAR(100) PRIMARY KEY,           -- Configuration key (primary key)
    value TEXT,                             -- Configuration value
    description TEXT,                       -- Human-readable description
    data_type VARCHAR(50) DEFAULT 'string', -- Value type: string, number, boolean, json
    category VARCHAR(100),                  -- Category for grouping
    created_at TIMESTAMP DEFAULT NOW(),     -- Creation timestamp
    updated_at TIMESTAMP DEFAULT NOW()      -- Last update timestamp
);

-- Indexes for performance
CREATE INDEX idx_sys_config_category ON sys_config(category);
CREATE INDEX idx_sys_config_data_type ON sys_config(data_type);
```

### Entity Structure
```typescript
@Entity('sys_config')
export class SysConfigEntity {
    @PrimaryColumn({ type: 'varchar', length: 100 })
    key!: string;

    @Column({ type: 'text', nullable: true })
    value?: string | null;

    @Column({ type: 'text', nullable: true })
    description?: string | null;

    @Column({ type: 'varchar', length: 50, default: 'string' })
    dataType!: string; // 'string', 'number', 'boolean', 'json'

    @Column({ type: 'varchar', length: 100, nullable: true })
    category?: string | null;

    // Helper methods for type conversion
    getTypedValue<T = any>(): T;
    setTypedValue(value: any, dataType?: string): void;
}
```

## 🚀 Quick Start

### Basic Usage

```typescript
import { SysConfigService } from '@modules/sys-config/sys-config.service';

@Injectable()
export class YourService {
    constructor(private sysConfigService: SysConfigService) {}

    // Get typed configuration value
    async getMaxAttempts(): Promise<number> {
        return this.sysConfigService.getValue<number>('max_login_attempts');
    }

    // Set configuration with metadata
    async updateSetting(): Promise<void> {
        await this.sysConfigService.setValue({
            key: 'api_rate_limit',
            value: 100,
            description: 'API requests per minute limit',
            dataType: 'number',
            category: 'security'
        });
    }

    // Get configuration with fallback
    async getConfigWithFallback(): Promise<string> {
        try {
            return await this.sysConfigService.getValue<string>('site_name');
        } catch (error) {
            return 'Default Site Name';
        }
    }
}
```

### Module Integration

```typescript
import { SysConfigModule } from '@modules/sys-config/sys-config.module';

@Module({
    imports: [
        SysConfigModule, // Import the module
        // ... other modules
    ],
    providers: [YourService],
})
export class YourModule {}
```

## 🚀 API Endpoints

### Configuration Management (Admin Only)
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| `GET` | `/sys-config` | Get all configurations with filtering | ✅ Admin |
| `GET` | `/sys-config/:key` | Get single configuration by key | ✅ Admin |
| `GET` | `/sys-config/category/:category` | Get configurations by category | ✅ Admin |
| `POST` | `/sys-config` | Set/update configuration value | ✅ Admin |
| `POST` | `/sys-config/bulk` | Bulk set multiple configurations | ✅ Admin |
| `DELETE` | `/sys-config/:key` | Delete configuration | ✅ Admin |

### Cache Management (Admin Only)
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| `GET` | `/sys-config/stats/cache` | Get cache statistics | ✅ Admin |
| `POST` | `/sys-config/cache/clear` | Clear configuration cache | ✅ Admin |
| `POST` | `/sys-config/cache/preload` | Preload cache with all configs | ✅ Admin |

## 📊 Request/Response Examples

### 🔍 Get Single Configuration
**Request:**
```http
GET /sys-config/site_name
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 OK):**
```json
{
    "key": "site_name",
    "value": "FS Player Service",
    "dataType": "string"
}
```

### 📋 Get All Configurations
**Request:**
```http
GET /sys-config?category=security&includeSecrets=false
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 OK):**
```json
[
    {
        "key": "max_login_attempts",
        "value": "5",
        "description": "Maximum login attempts before lockout",
        "dataType": "number",
        "category": "security",
        "createdAt": "2025-08-16T10:30:00Z",
        "updatedAt": "2025-08-16T10:30:00Z",
        "typedValue": 5
    }
]
```

### 📂 Get Configurations by Category
**Request:**
```http
GET /sys-config/category/security
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 OK):**
```json
[
    {
        "key": "max_login_attempts",
        "value": "5",
        "description": "Maximum login attempts before lockout",
        "dataType": "number",
        "category": "security",
        "createdAt": "2025-08-16T10:30:00Z",
        "updatedAt": "2025-08-16T10:30:00Z",
        "typedValue": 5
    }
]
```

### ✏️ Set Configuration
**Request:**
```http
POST /sys-config
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
    "key": "maintenance_mode",
    "value": false,
    "description": "Enable/disable maintenance mode",
    "dataType": "boolean",
    "category": "system"
}
```

**Response (200 OK):**
```json
{
    "key": "maintenance_mode",
    "value": "false",
    "description": "Enable/disable maintenance mode",
    "dataType": "boolean",
    "category": "system",
    "createdAt": "2025-08-16T10:30:00Z",
    "updatedAt": "2025-08-16T10:30:00Z",
    "typedValue": false
}
```

### 📦 Bulk Set Configurations
**Request:**
```http
POST /sys-config/bulk
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

[
    {
        "key": "api_rate_limit",
        "value": 100,
        "dataType": "number",
        "category": "security"
    },
    {
        "key": "cache_ttl",
        "value": 3600,
        "dataType": "number",
        "category": "performance"
    }
]
```

**Response (200 OK):**
```json
[
    {
        "key": "api_rate_limit",
        "value": "100",
        "dataType": "number",
        "category": "security",
        "typedValue": 100
    },
    {
        "key": "cache_ttl",
        "value": "3600",
        "dataType": "number",
        "category": "performance",
        "typedValue": 3600
    }
]
```

### 🗑️ Delete Configuration
**Request:**
```http
DELETE /sys-config/old_setting
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 OK):**
```json
{
    "status": "success",
    "message": "Configuration deleted successfully"
}
```

### 📊 Get Cache Statistics
**Request:**
```http
GET /sys-config/stats/cache
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 OK):**
```json
{
    "totalCached": 150,
    "hitCount": 1250,
    "missCount": 50,
    "hitRatio": 96.15
}
```

### 🧹 Clear Cache
**Request:**
```http
POST /sys-config/cache/clear
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 OK):**
```json
{
    "status": "success",
    "message": "Configuration cache cleared successfully"
}
```

### ⚡ Preload Cache
**Request:**
```http
POST /sys-config/cache/preload
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 OK):**
```json
{
    "status": "success",
    "message": "Configuration cache preloaded successfully"
}
```

## 🔧 Configuration Categories

### System Configurations
```json
{
    "site_name": "FS Player Service",
    "site_description": "Full Stack Player Service API",
    "maintenance_mode": false,
    "cache_ttl": 3600
}
```

### Security Configurations
```json
{
    "max_login_attempts": 5,
    "max_ip_login_attempts": 20,
    "session_timeout": 3600,
    "api_rate_limit": 100
}
```

### OTP Configurations
```json
{
    "otp_ttl": 5,
    "otp_limit_ttl": 5,
    "otp_max_limit_user": 3,
    "otp_max_limit_ip": 10,
    "otp_length": 6,
    "otp_max_attempts": 3
}
```

### Integration Configurations
```json
{
    "ZALO_ACCESS_TOKEN": "your-zalo-access-token",
    "ZALO_REFRESH_TOKEN": "your-zalo-refresh-token",
    "email_settings": {
        "smtp_host": "localhost",
        "smtp_port": 587,
        "use_tls": true
    }
}
```

## 🎯 Data Types

### Supported Types
- **`string`** - Text values (default)
- **`number`** - Numeric values with automatic conversion
- **`boolean`** - True/false values with string parsing
- **`json`** - Complex objects with JSON parsing/validation

### Type Conversion Examples
```typescript
// String (default)
await sysConfigService.setValue({
    key: 'site_name',
    value: 'My Website',
    dataType: 'string'
});

// Number
await sysConfigService.setValue({
    key: 'max_attempts',
    value: 5,
    dataType: 'number'
});

// Boolean
await sysConfigService.setValue({
    key: 'maintenance_mode',
    value: false,
    dataType: 'boolean'
});

// JSON
await sysConfigService.setValue({
    key: 'email_config',
    value: { host: 'smtp.gmail.com', port: 587 },
    dataType: 'json'
});
```

## ⚡ Caching Strategy

### Redis Caching
- **Cache Key Pattern:** `sys_config:{key}`
- **TTL:** 1 hour (3600 seconds)
- **Strategy:** Cache-aside with automatic fallback
- **Statistics:** Real-time hit/miss tracking

### Cache Flow
```
1. Request → Check Redis Cache
2. Cache Hit → Return cached value
3. Cache Miss → Query Database
4. Store in Cache → Return value
5. Update Stats → Track performance
```

### Performance Benefits
- **96%+ Cache Hit Ratio** - Typical performance
- **Sub-millisecond Response** - Cached requests
- **Automatic Fallback** - Graceful degradation
- **Statistics Tracking** - Performance monitoring

## 🔒 Security Features

### Role-Based Access Control
- **Admin Only Access**: All endpoints require admin authentication
- **No Public Endpoints**: All configuration access restricted to admins
- **JWT Authentication**: Bearer token required for all operations

### Security Best Practices
- **Secret Masking** - Sensitive values hidden in API responses
- **Admin Authorization** - All operations require admin role
- **Audit Logging** - Configuration changes tracked
- **Input Validation** - Type validation before storage

## 🚨 Error Handling

### HTTP Status Codes
| Status Code | Description | Common Scenarios |
|-------------|-------------|------------------|
| `400` | Bad Request | Invalid data type, malformed JSON |
| `401` | Unauthorized | Missing or invalid authentication token |
| `403` | Forbidden | Non-admin user accessing endpoints |
| `404` | Not Found | Configuration key not found |
| `500` | Internal Server Error | Database or cache errors |

### Error Response Format
All error responses follow this structure:
```json
{
    "statusCode": 404,
    "message": "Configuration key \"invalid_key\" not found",
    "error": "Not Found",
    "timestamp": "2025-08-16T10:30:00Z",
    "path": "/sys-config/invalid_key"
}
```

### Common Error Examples

**Configuration Not Found (404):**
```json
{
    "statusCode": 404,
    "message": "Configuration key \"invalid_key\" not found",
    "error": "Not Found"
}
```

**Invalid Data Type (400):**
```json
{
    "statusCode": 400,
    "message": "Value \"abc\" is not a valid number",
    "error": "Bad Request"
}
```

**Invalid JSON (400):**
```json
{
    "statusCode": 400,
    "message": "Value \"{invalid json\" is not valid JSON",
    "error": "Bad Request"
}
```

**Admin Access Required (403):**
```json
{
    "statusCode": 403,
    "message": "Forbidden resource",
    "error": "Forbidden"
}
```

## 🧪 Testing

### Unit Tests
```typescript
describe('SysConfigService', () => {
    let service: SysConfigService;
    let mockRedisService: jest.Mocked<RedisService>;

    beforeEach(async () => {
        const module = await Test.createTestingModule({
            providers: [
                SysConfigService,
                {
                    provide: RedisService,
                    useValue: mockRedisService,
                },
                {
                    provide: getRepositoryToken(SysConfigEntity),
                    useValue: mockRepository,
                },
            ],
        }).compile();

        service = module.get<SysConfigService>(SysConfigService);
    });

    it('should get typed value with cache', async () => {
        mockRedisService.get.mockResolvedValue({
            key: 'test_key',
            value: '5',
            dataType: 'number'
        });

        const result = await service.getValue<number>('test_key');
        expect(result).toBe(5);
        expect(mockRedisService.get).toHaveBeenCalledWith('sys_config:test_key');
    });

    it('should fallback to database on cache miss', async () => {
        mockRedisService.get.mockResolvedValue(null);
        mockRepository.findOne.mockResolvedValue({
            key: 'test_key',
            value: 'test_value',
            dataType: 'string'
        });

        const result = await service.getValue('test_key');
        expect(result).toBe('test_value');
    });
});
```

### Integration Tests
```typescript
describe('SysConfig API', () => {
    it('should get configuration value', async () => {
        const response = await request(app.getHttpServer())
            .get('/sys-config/site_name')
            .expect(200);

        expect(response.body).toEqual({
            key: 'site_name',
            value: 'FS Player Service',
            dataType: 'string'
        });
    });

    it('should require admin role for write operations', async () => {
        await request(app.getHttpServer())
            .post('/sys-config')
            .send({
                key: 'test_key',
                value: 'test_value'
            })
            .expect(401);
    });
});
```

## 📊 Monitoring & Analytics

### Cache Performance Metrics
```typescript
// Get real-time cache statistics
const stats = await sysConfigService.getCacheStats();
console.log(`Cache Hit Ratio: ${stats.hitRatio}%`);
console.log(`Total Cached: ${stats.totalCached} configs`);
```

### Performance Monitoring
- **Cache Hit Ratio** - Target: >95%
- **Response Time** - Cached: <1ms, DB: <10ms
- **Memory Usage** - Redis cache size monitoring
- **Error Rate** - Configuration access failures

### Alerting Thresholds
- Cache hit ratio < 90%
- Response time > 100ms
- Error rate > 1%
- Cache size > 100MB

## 🔄 Migration Guide

### From Direct Entity Access
```typescript
// ❌ Before - Direct repository access
@InjectRepository(SysConfigEntity)
private configRepository: Repository<SysConfigEntity>;

async getConfig(key: string): Promise<string> {
    const config = await this.configRepository.findOne({ where: { key } });
    return config?.value || '';
}

// ✅ After - Service layer access
constructor(private sysConfigService: SysConfigService) {}

async getConfig(key: string): Promise<string> {
    try {
        return await this.sysConfigService.getValue<string>(key);
    } catch (error) {
        return ''; // fallback value
    }
}
```

### Module Updates
```typescript
// ❌ Before
@Module({
    imports: [TypeOrmModule.forFeature([SysConfigEntity])],
})

// ✅ After
@Module({
    imports: [SysConfigModule],
})
```

## 🚀 Performance Optimization

### Best Practices
1. **Use Type-Safe Access** - `getValue<T>()` for automatic conversion
2. **Implement Fallbacks** - Always provide default values
3. **Cache Preloading** - Warm cache on application startup
4. **Batch Operations** - Use bulk endpoints for multiple configs
5. **Monitor Performance** - Track cache statistics regularly

### Optimization Tips
```typescript
// ✅ Good - Type-safe with fallback
private async getAuthConfig<T>(key: string, defaultValue: T): Promise<T> {
    try {
        return await this.sysConfigService.getValue<T>(key);
    } catch (error) {
        return defaultValue;
    }
}

// ✅ Good - Batch operations
await this.sysConfigService.bulkSet([
    { key: 'setting1', value: 'value1' },
    { key: 'setting2', value: 'value2' }
]);

// ✅ Good - Preload critical configs
await this.sysConfigService.preloadCache();
```

## 📚 Additional Resources

### Related Documentation
- [API Documentation](http://localhost:4001/documentation) - Interactive Swagger UI
- [Database Migrations](../../database/migrations/) - Schema updates
- [Redis Configuration](../../shared/modules/redis.module.ts) - Cache setup

### External Links
- [NestJS Configuration](https://docs.nestjs.com/techniques/configuration)
- [TypeORM Entity Documentation](https://typeorm.io/entities)
- [Redis Caching Strategies](https://redis.io/docs/manual/patterns/)

## 🤝 Contributing

### Development Setup
1. Install dependencies: `npm install`
2. Start Redis: `docker run -d -p 6379:6379 redis:alpine`
3. Run migrations: `npm run migration:run`
4. Start development: `npm run start:dev`

### Code Standards
- Follow existing TypeScript patterns
- Add comprehensive tests for new features
- Update documentation for API changes
- Ensure cache invalidation strategies

---

**Built with ❤️ for enterprise-grade configuration management**
