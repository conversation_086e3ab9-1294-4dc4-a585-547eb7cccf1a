import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
	IsIn,
	IsNotEmpty,
	IsOptional,
	IsString,
	MaxLength,
} from 'class-validator';

export class SysConfigDto {
	@ApiProperty({
		example: 'site_name',
		description: 'The configuration key (primary key)',
		maxLength: 100,
	})
	key!: string;

	@ApiPropertyOptional({
		example: 'My Website',
		description: 'The configuration value',
	})
	value?: string | null;

	@ApiPropertyOptional({
		example: 'Website name displayed in the header',
		description: 'Description of what this config does',
	})
	description?: string | null;

	@ApiProperty({
		example: 'string',
		description: 'Data type of the value',
		enum: ['string', 'number', 'boolean', 'json'],
	})
	dataType!: string;

	@ApiPropertyOptional({
		example: 'general',
		description: 'Category to group related configs',
	})
	category?: string | null;

	@ApiProperty({
		example: '2025-08-16T10:30:00Z',
		description: 'Creation timestamp',
	})
	createdAt!: Date;

	@ApiProperty({
		example: '2025-08-16T10:30:00Z',
		description: 'Last update timestamp',
	})
	updatedAt!: Date;

	@ApiPropertyOptional({
		description: 'Value converted to appropriate type based on dataType',
	})
	typedValue?: any;
}

export class SetConfigValueDto {
	@ApiProperty({
		example: 'site_name',
		description: 'The configuration key (max 100 characters)',
		maxLength: 100,
	})
	@IsString()
	@IsNotEmpty()
	@MaxLength(100)
	key!: string;

	@ApiPropertyOptional({
		example: 'My Website',
		description: 'The configuration value',
	})
	@IsOptional()
	value?: any;

	@ApiPropertyOptional({
		example: 'Website name displayed in the header',
		description: 'Description of what this config does',
	})
	@IsOptional()
	@IsString()
	description?: string;

	@ApiPropertyOptional({
		example: 'string',
		description: 'Data type of the value',
		enum: ['string', 'number', 'boolean', 'json'],
		default: 'string',
	})
	@IsOptional()
	@IsString()
	@IsIn(['string', 'number', 'boolean', 'json'])
	dataType?: string;

	@ApiPropertyOptional({
		example: 'general',
		description: 'Category to group related configs',
	})
	@IsOptional()
	@IsString()
	@MaxLength(100)
	category?: string;
}

export class GetConfigValueDto {
	@ApiProperty({
		example: 'site_name',
		description: 'The configuration key',
	})
	key!: string;

	@ApiProperty({
		example: 'My Website',
		description: 'The configuration value',
	})
	value!: any;

	@ApiPropertyOptional({
		example: 'string',
		description: 'Data type of the value',
	})
	dataType?: string;
}

export class ConfigCacheStatsDto {
	@ApiProperty({
		example: 150,
		description: 'Total number of configs in cache',
	})
	totalCached!: number;

	@ApiProperty({
		example: 1250,
		description: 'Cache hit count',
	})
	hitCount!: number;

	@ApiProperty({
		example: 50,
		description: 'Cache miss count',
	})
	missCount!: number;

	@ApiProperty({
		example: 96.15,
		description: 'Cache hit ratio percentage',
	})
	hitRatio!: number;
}
