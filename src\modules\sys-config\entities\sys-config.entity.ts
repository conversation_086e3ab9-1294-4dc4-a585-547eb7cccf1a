import { UseDto } from '@decorators/use-dto.decorator';
import {
	Column,
	CreateDateColumn,
	Entity,
	PrimaryColumn,
	UpdateDateColumn,
} from 'typeorm';

import { SysConfigDto } from '../dto/sys-config.dto';

@Entity('sys_config')
@UseDto(SysConfigDto)
export class SysConfigEntity {
	@PrimaryColumn({ type: 'varchar', length: 100 })
	key!: string;

	@Column({ type: 'text', nullable: true })
	value?: string | null;

	@Column({ type: 'text', nullable: true })
	description?: string | null;

	@Column({ type: 'varchar', length: 50, default: 'string' })
	dataType!: string; // 'string', 'number', 'boolean', 'json'

	@Column({ type: 'varchar', length: 100, nullable: true })
	category?: string | null; // Group related configs

	@CreateDateColumn({ name: 'created_at' })
	createdAt!: Date;

	@UpdateDateColumn({ name: 'updated_at' })
	updatedAt!: Date;

	// Helper method to get typed value
	getTypedValue<T = any>(): T {
		if (!this.value) return null as T;

		switch (this.dataType) {
			case 'number':
				return Number(this.value) as T;
			case 'boolean':
				return (this.value.toLowerCase() === 'true') as T;
			case 'json':
				try {
					return JSON.parse(this.value) as T;
				} catch {
					return this.value as T;
				}
			default:
				return this.value as T;
		}
	}

	// Helper method to set typed value
	setTypedValue(value: any, dataType?: string): void {
		if (dataType) {
			this.dataType = dataType;
		}

		if (value === null || value === undefined) {
			this.value = null;
			return;
		}

		switch (this.dataType) {
			case 'json':
				this.value = typeof value === 'string' ? value : JSON.stringify(value);
				break;
			case 'boolean':
				this.value = Boolean(value).toString();
				break;
			case 'number':
				this.value = Number(value).toString();
				break;
			default:
				this.value = String(value);
		}
	}

	toDto(): SysConfigDto {
		return {
			key: this.key,
			value: this.value,
			description: this.description,
			dataType: this.dataType,
			category: this.category,
			createdAt: this.createdAt,
			updatedAt: this.updatedAt,
			typedValue: this.getTypedValue(),
		};
	}
}
