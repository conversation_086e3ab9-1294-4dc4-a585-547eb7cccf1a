import {
	Body,
	Controller,
	Delete,
	Get,
	HttpCode,
	HttpStatus,
	Param,
	Post,
	Query,
} from '@nestjs/common';
import {
	ApiBadRequestResponse,
	ApiBearerAuth,
	ApiBody,
	ApiForbiddenResponse,
	ApiNotFoundResponse,
	ApiOperation,
	ApiParam,
	ApiQuery,
	ApiTags,
	ApiUnauthorizedResponse,
} from '@nestjs/swagger';

import { RoleType } from '../../constants/role-type';
import { ApiCommonResponse } from '../../decorators/api-common-response.decorator';
import { Auth } from '../../decorators/http.decorators';
import {
	ConfigCacheStatsDto,
	GetConfigValueDto,
	SetConfigValueDto,
	SysConfigDto,
} from './dto/sys-config.dto';
import { SysConfigService } from './sys-config.service';

@ApiTags('System Configuration (Admin Only)')
@ApiBearerAuth()
@Controller('sys-config')
export class SysConfigController {
	constructor(private readonly sysConfigService: SysConfigService) {}

	@Get('stats/cache')
	@Auth([RoleType.ADMIN])
	@ApiOperation({
		summary: 'Get cache statistics (Admin Only)',
		description:
      'Get detailed cache performance statistics for system configurations. Requires admin authentication.',
	})
	@ApiCommonResponse({
		type: ConfigCacheStatsDto,
		description: 'Cache statistics retrieved successfully',
		examples: {
			default: {
				value: {
					totalCached: 150,
					hitCount: 1250,
					missCount: 50,
					hitRatio: 96.15,
				},
				summary: '',
			},
		},
	})
	@ApiUnauthorizedResponse({ description: 'Authentication required' })
	@ApiForbiddenResponse({ description: 'Admin role required' })
	async getCacheStats(): Promise<ConfigCacheStatsDto> {
		return this.sysConfigService.getCacheStats();
	}

	@Post('cache/clear')
	@Auth([RoleType.ADMIN])
	@HttpCode(HttpStatus.OK)
	@ApiOperation({
		summary: 'Clear configuration cache (Admin Only)',
		description:
      'Clear all cached configuration values. Use with caution as it may impact performance temporarily. Requires admin authentication.',
	})
	@ApiCommonResponse({ description: 'Cache cleared successfully' })
	async clearCache(): Promise<void> {
		await this.sysConfigService.clearCache();
	}

	@Post('cache/preload')
	@Auth([RoleType.ADMIN])
	@HttpCode(HttpStatus.OK)
	@ApiOperation({
		summary: 'Preload configuration cache (Admin Only)',
		description:
      'Load all configurations into cache for better performance. Requires admin authentication.',
	})
	@ApiCommonResponse({ description: 'Cache preloaded successfully' })
	async preloadCache(): Promise<void> {
		await this.sysConfigService.preloadCache();
	}

	@Get('category/:category')
	@Auth([RoleType.ADMIN])
	@ApiOperation({
		summary: 'Get configurations by category (Admin Only)',
		description:
      'Retrieve all configuration values for a specific category. Requires admin authentication.',
	})
	@ApiParam({
		name: 'category',
		description: 'Configuration category',
		example: 'general',
	})
	@ApiCommonResponse({
		type: [SysConfigDto],
		description: 'Configurations retrieved successfully',
	})
	@ApiNotFoundResponse({
		description: 'No configurations found for the specified category',
	})
	async getByCategory(
		@Param('category') category: string,
	): Promise<SysConfigDto[]> {
		const configs = await this.sysConfigService.getByCategory(category);
		return configs.map((config) => config.toDto());
	}

	@Get(':key')
	@Auth([RoleType.ADMIN])
	@ApiOperation({
		summary: 'Get configuration value by key (Admin Only)',
		description:
      'Retrieve a specific configuration value. Secret values will be masked. Requires admin authentication.',
	})
	@ApiParam({
		name: 'key',
		description: 'Configuration key',
		example: 'site_name',
	})
	@ApiCommonResponse({
		type: GetConfigValueDto,
		description: 'Configuration value retrieved successfully',
		examples: {
			default: {
				value: {
					key: 'site_name',
					value: 'My Website',
					dataType: 'string',
				},
				summary: '',
			},
		},
	})
	@ApiNotFoundResponse({ description: 'Configuration key not found' })
	async getValue(@Param('key') key: string): Promise<GetConfigValueDto> {
		return this.sysConfigService.getConfigForApi(key);
	}

	@Get()
	@Auth([RoleType.ADMIN])
	@ApiOperation({
		summary: 'Get all configurations (Admin Only)',
		description:
      'Retrieve all configuration values with optional filtering. Requires admin authentication.',
	})
	@ApiQuery({
		name: 'category',
		required: false,
		description: 'Filter by category',
		example: 'general',
	})
	@ApiCommonResponse({
		type: [SysConfigDto],
		description: 'Configurations retrieved successfully',
	})
	async findAll(@Query('category') category?: string): Promise<SysConfigDto[]> {
		const configs = await this.sysConfigService.findAll(category);
		return configs.map((config) => config.toDto());
	}

	@Post()
	@Auth([RoleType.ADMIN])
	@HttpCode(HttpStatus.OK)
	@ApiOperation({
		summary: 'Set configuration value',
		description:
      'Create or update a configuration value with type validation and caching',
	})
	@ApiBody({ type: SetConfigValueDto })
	@ApiCommonResponse({
		type: SysConfigDto,
		description: 'Configuration set successfully',
	})
	@ApiBadRequestResponse({
		description: 'Invalid input data or type validation failed',
	})
	async setValue(@Body() dto: SetConfigValueDto): Promise<SysConfigDto> {
		const config = await this.sysConfigService.setValue(dto);
		return config.toDto();
	}

	@Post('bulk')
	@Auth([RoleType.ADMIN])
	@HttpCode(HttpStatus.OK)
	@ApiOperation({
		summary: 'Bulk set configurations',
		description: 'Set multiple configuration values in a single request',
	})
	@ApiBody({
		type: [SetConfigValueDto],
		description: 'Array of configuration values to set',
	})
	@ApiCommonResponse({
		type: [SysConfigDto],
		description: 'Configurations set successfully',
	})
	async bulkSet(@Body() configs: SetConfigValueDto[]): Promise<SysConfigDto[]> {
		const results = await this.sysConfigService.bulkSet(configs);
		return results.map((config) => config.toDto());
	}

	@Delete(':key')
	@Auth([RoleType.ADMIN])
	@HttpCode(HttpStatus.OK)
	@ApiOperation({
		summary: 'Delete configuration',
		description: 'Delete a configuration value and clear its cache',
	})
	@ApiParam({
		name: 'key',
		description: 'Configuration key to delete',
		example: 'old_setting',
	})
	@ApiCommonResponse({ description: 'Configuration deleted successfully' })
	@ApiNotFoundResponse({ description: 'Configuration key not found' })
	async deleteConfig(@Param('key') key: string): Promise<void> {
		await this.sysConfigService.deleteConfig(key);
	}
}