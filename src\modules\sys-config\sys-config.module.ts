import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SharedModule } from '@shared/shared.module';

import { SysConfigEntity } from './entities/sys-config.entity';
import { SysConfigController } from './sys-config.controller';
import { SysConfigService } from './sys-config.service';

@Module({
	imports: [
		TypeOrmModule.forFeature([SysConfigEntity]),
		SharedModule, // RedisService is available through SharedModule
	],
	controllers: [SysConfigController],
	providers: [SysConfigService],
	exports: [SysConfigService], // Export service for use in other modules
})
export class SysConfigModule {}
