# User Management Module

## 📋 Overview

The User module handles user account management, profile management, and user-related operations for the FS Player Service. It provides user profile operations, quickplay account system, social account management, and transaction history.

## 🔧 Features

### User Account Management
- **Profile Management** - User profiles with customizable information
- **Account Linking** - Link quickplay accounts to permanent accounts
- **Social Account Management** - Link/unlink social accounts
- **Transaction History** - User payment transaction records

### Quickplay System
- **Anonymous Gaming** - Temporary accounts for quick access
- **Account Conversion** - Convert quickplay to permanent accounts

### Social Integration
- **Social Account Info** - Get linked social account information
- **Account Unlinking** - Remove social account connections

## 🚀 API Endpoints

### User Profile
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| `GET` | `/user/profile` | Get current user profile | ✅ |
| `PUT` | `/user/profile` | Update user profile | ✅ |

### Quickplay Operations
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| `POST` | `/user/quickplay` | Create/login quickplay account | ✅ |
| `POST` | `/user/quickplay/link` | Link quickplay to permanent account | ✅ |

### Social Account Management
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| `GET` | `/user/social/account` | Get social account info | ✅ |
| `DELETE` | `/user/social/account/:provider` | Unlink social account | ✅ |

### Transaction History
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| `GET` | `/user/transactions/history` | Get user transaction history | ✅ |

## 📊 Data Models

### User Account Entity
```typescript
{
  userId: number,              // Primary key
  username: string,            // Unique username
  email: string,               // Unique email
  phone: string,               // Unique phone number
  passwordHash: string,        // Bcrypt hashed password
  status: UserAccountStatus,   // active, inactive, banned, pending_verification
  accountType: UserAccountType, // local, facebook, google, apple, quickplay
  socialUid: string,           // Social platform user ID
  refreshToken: string,        // JWT refresh token
  createdAt: Date,
  updatedAt: Date
}
```

### User Profile Entity
```typescript
{
  profileId: number,           // Primary key
  userId: number,              // Foreign key to user_account
  displayName: string,         // Display name
  gender: 'female' | 'male' | 'other',  // Gender enum
  avatarUrl: string,           // Profile picture URL
  dob: Date,                   // Date of birth
  address: string,             // User address
  identifierNumber: string,    // ID number for verification
  createdAt: Date,
  updatedAt: Date
}
```

## 📊 Request/Response Examples

### 👤 Get User Profile
**Request:**
```http
GET /user/profile
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 OK):**
```json
{
  "userId": 123,
  "username": "john_doe",
  "email": "<EMAIL>",
  "status": "ACTIVE",
  "accountType": "REGULAR",
  "userBalance": 1000,
  "createdAt": "2025-08-16T10:30:00Z",
  "updatedAt": "2025-08-16T10:30:00Z",
  "lastLoginAt": "2025-08-16T10:30:00Z",
  "createdAtIp": "***********",
  "lastLoginAtIp": "***********"
}
```

### ✏️ Update User Profile
**Request:**
```http
PUT /user/profile
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "displayName": "John Smith",
  "gender": "male",
  "dob": "1990-01-01",
  "avatarUrl": "https://example.com/avatar.jpg",
  "address": "123 Main St, Ho Chi Minh City",
  "identifierNumber": "*********"
}
```

**Response (202 Accepted):**
```json
{
  "status": "success",
  "message": "Profile updated successfully",
  "data": null
}
```

### 🎮 Create/Login Quickplay Account
**Request:**
```http
POST /user/quickplay
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "username": "qp_existing_username",
  "uniqueId": "ANDROID_ID_or_IDFV",
  "deviceId": "device_unique_identifier",
  "platform": "ANDROID"
}
```

**Response (200 OK):**
```json
{
  "qpId": "qp_uuid_generated",
  "qpToken": "ANDROID_ID_or_IDFV",
  "createdAt": "2025-08-16T10:30:00Z"
}
```

### 🔗 Link Quickplay Account
**Request:**
```http
POST /user/quickplay/link
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "username": "new_username",
  "uniqueId": "ANDROID_ID_or_IDFV",
  "deviceId": "device_unique_identifier",
  "platform": "ANDROID",
  "password": "Password123!",
  "qpId": "qp_uuid_generated",
  "qpToken": "ANDROID_ID_or_IDFV"
}
```

**Response (200 OK):**
```json
{
  "status": "success",
  "message": "Quickplay account linked successfully",
  "data": null
}
```

## 🔧 Account Types

### Local Account
- **Registration**: Email/phone + password
- **Authentication**: Local credentials
- **Features**: Full account features

### Social Accounts
- **Facebook**: OAuth integration
- **Google**: OAuth integration  
- **Apple**: Sign in with Apple
- **Auto-creation**: Created on first social login

### Quickplay Account
- **Purpose**: Anonymous gaming
- **Creation**: Device-based unique ID
- **Limitations**: Temporary, limited features
- **Conversion**: Can be linked to permanent account

## 📊 User Status Management

### Account Statuses
```typescript
enum UserAccountStatus {
  ACTIVE = 'active',                    // Normal active account
  INACTIVE = 'inactive',                // Temporarily disabled
  BANNED = 'banned',                    // Permanently banned
  PENDING_VERIFICATION = 'pending_verification'  // Awaiting verification
}
```

### Status Transitions
- **Registration** → `PENDING_VERIFICATION`
- **Email/Phone Verified** → `ACTIVE`
- **Admin Action** → `BANNED` or `INACTIVE`
- **Account Recovery** → `ACTIVE`

## 🔍 Search and Filtering

### User Search
```typescript
// Find by email or username
const user = await userService.findByUsernameOrEmail({
  email: '<EMAIL>',
  username: 'user123'
});
```

### 📱 Get Social Account Info
**Request:**
```http
GET /user/social/account
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 OK):**
```json
{
  "socialUid": "facebook_user_id_123",
  "name": "John Doe",
  "email": "<EMAIL>",
  "avatarUrl": "https://graph.facebook.com/123/picture",
  "provider": "FACEBOOK"
}
```

### 🔗 Unlink Social Account
**Request:**
```http
DELETE /user/social/account/facebook
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 OK):**
```json
{
  "status": "success",
  "message": "Social account unlinked successfully",
  "data": null
}
```

### 💰 Get Transaction History
**Request:**
```http
GET /user/transactions/history?page=1&take=10&filterStatus=SUCCESS&dateFrom=2025-01-01&dateTo=2025-12-31
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Query Parameters:**
- `page`: Page number (default: 1)
- `take`: Items per page (default: 10, max: 50)
- `order`: Sort order (ASC/DESC, default: ASC)
- `q`: Search query (optional)
- `filterStatus`: Filter by payment status (SUCCESS, PENDING, FAILED, etc.)
- `dateFrom`: Start date filter (ISO date)
- `dateTo`: End date filter (ISO date)

**Response (200 OK):**
```json
{
  "data": [
    {
      "txId": 123,
      "gameId": 456,
      "orderId": "ORDER_123456",
      "amount": 50000,
      "currency": "VND",
      "paymentMethod": "MOMO",
      "status": "SUCCESS",
      "note": "Game purchase",
      "createdAt": "2025-08-16T10:30:00Z"
    }
  ],
  "meta": {
    "page": 1,
    "take": 10,
    "itemCount": 1,
    "pageCount": 1,
    "hasPreviousPage": false,
    "hasNextPage": false
  }
}
```

## 🔒 Security Features

### Data Protection
- **Password Hashing**: Bcrypt with salt
- **Sensitive Data**: Masked in API responses
- **Access Control**: User can only access own data
- **Admin Override**: Admin can access all user data

### Validation
- **Email Format**: RFC compliant email validation
- **Phone Format**: Vietnamese phone number validation
- **Username**: Unique constraint enforcement
- **Profile Data**: Input sanitization and validation

## 🚨 Error Handling

### HTTP Status Codes
| Status Code | Description | Common Scenarios |
|-------------|-------------|------------------|
| `400` | Bad Request | Invalid request format, validation errors |
| `401` | Unauthorized | Missing or invalid authentication token |
| `403` | Forbidden | Insufficient permissions |
| `404` | Not Found | User not found, resource not found |
| `409` | Conflict | Username/email already exists |
| `500` | Internal Server Error | Server-side errors |

### Error Response Format
All error responses follow this structure:
```json
{
  "statusCode": 404,
  "message": "User not found",
  "error": "Not Found",
  "timestamp": "2025-08-16T10:30:00Z",
  "path": "/user/profile"
}
```

### Common Error Examples

**User Not Found (404):**
```json
{
  "statusCode": 404,
  "message": "User not found",
  "error": "Not Found"
}
```

**Validation Error (400):**
```json
{
  "statusCode": 400,
  "message": ["displayName must be a string"],
  "error": "Bad Request"
}
```

**Social Account Error (400):**
```json
{
  "statusCode": 400,
  "message": "Tài khoản này không phải là tài khoản đăng nhập mạng xã hội.",
  "error": "Bad Request"
}
```

**Quickplay Link Error (409):**
```json
{
  "statusCode": 409,
  "message": "Tài khoản này đã được liên kết trước đó",
  "error": "Conflict"
}
```

## 🔧 Configuration

### Caching
- **User Data**: Redis cache with 30-minute TTL
- **Cache Keys**: `user:${userId}`
- **Cache Invalidation**: On profile updates

### Database Relations
- **User Account** ↔ **User Profile** (One-to-One)
- **User Account** ↔ **Payment Transactions** (One-to-Many)
- **User Account** ↔ **Audit Logs** (One-to-Many)
- **User Account** ↔ **Game Mappings** (One-to-Many)

## 📝 Notes

- User profiles are automatically created with user accounts
- Quickplay accounts have limited functionality
- Social accounts are auto-verified
- Admin users are identified by JWT role, not database field
- Transaction history includes payment and game-related transactions
- User data is cached for performance optimization

## 🔗 Related Modules

- [Auth Module](../auth/README.md) - Authentication and authorization
- [Payment Module](../payment/README.md) - Payment transactions
- [Game Module](../game/README.md) - Game-related user data
- [Audit Log Module](../audit-log/README.md) - User activity tracking
