import type { ICommand, ICommandHandler } from '@nestjs/cqrs';
import { CommandHandler } from '@nestjs/cqrs';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import type { MutateUserProfileDto } from '../dtos/mutate-user-profile.dto.ts';
import { UserProfileEntity } from '../user-profile.entity.ts';

export class CreateUserProfileCommand implements ICommand {
	constructor(
		public readonly userId: number,
		public readonly mutateUserProfileDto: MutateUserProfileDto,
	) {}
}

@CommandHandler(CreateUserProfileCommand)
export class CreateUserProfileHandler
implements ICommandHandler<CreateUserProfileCommand, UserProfileEntity> {
	constructor(
		@InjectRepository(UserProfileEntity)
		private userProfileRepository: Repository<UserProfileEntity>,
	) {}

	execute(command: CreateUserProfileCommand) {
		const { userId, mutateUserProfileDto } = command;
		const userProfileEntity =
			this.userProfileRepository.create(mutateUserProfileDto);

		userProfileEntity.userId = userId;

		return this.userProfileRepository.save(userProfileEntity);
	}
}
