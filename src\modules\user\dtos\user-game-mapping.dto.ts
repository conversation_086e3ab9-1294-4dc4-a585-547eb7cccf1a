import { AbstractDto } from '@common/dto/abstract.dto';
import {
	Date<PERSON><PERSON>,
	NumberField,
	StringField,
} from '@decorators/field.decorators';

import type { UserGameMappingEntity } from '../user-game-mapping.entity';

export class UserGameMappingDto extends AbstractDto {
	@StringField()
	ugmId: number;

	@StringField()
	userId: number;

	@NumberField()
	gameId: number;

	@NumberField()
	gameBalance: number;

	@DateField()
	assignedAt: Date;

	@DateField()
	createdAt: Date;

	@DateField()
	updatedAt: Date;

	constructor(userGameMapping: UserGameMappingEntity) {
		super(userGameMapping);
		this.ugmId = userGameMapping.ugmId;
		this.userId = userGameMapping.userId;
		this.gameId = userGameMapping.gameId;
		this.gameBalance = userGameMapping.gameBalance;
		this.assignedAt = userGameMapping.assignedAt;
		this.createdAt = userGameMapping.createdAt;
		this.updatedAt = userGameMapping.updatedAt;
	}
}
