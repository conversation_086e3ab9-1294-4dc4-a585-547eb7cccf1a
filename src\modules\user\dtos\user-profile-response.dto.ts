import { AbstractDto } from '@common/dto/abstract.dto';
import { maskEmail, maskId } from '@common/utils';
import { Gender } from '@constants/gender';
import { UserAccountStatus, UserAccountType } from '@constants/user';
import {
	<PERSON><PERSON>anField,
	DateField,
	DateFieldOptional,
	EmailFieldOptional,
	EnumField,
	EnumFieldOptional,
	NumberField,
	StringFieldOptional,
	VietnamesePhoneFieldOptional,
} from '@decorators/field.decorators';

import type { UserAccountEntity } from '../user-account.entity';

export class UserProfileResponseDto extends AbstractDto {
	// User Account fields
	@NumberField()
	userId!: number;

	@StringFieldOptional({ nullable: true })
	username?: string | null;

	@EmailFieldOptional({ nullable: true })
	email?: string | null;

	@VietnamesePhoneFieldOptional({ nullable: true })
	phone?: string | null;

	@BooleanField()
	isEmailVerified!: boolean;

	@BooleanField()
	isPhoneVerified!: boolean;

	@EnumField(() => UserAccountStatus)
	status: UserAccountStatus;

	@EnumField(() => UserAccountType)
	accountType: UserAccountType;

	@NumberField()
	userBalance!: number;

	@DateField()
	declare createdAt: Date;

	@DateField()
	declare updatedAt: Date;

	// User Profile fields
	@NumberField()
	profileId!: number;

	@StringFieldOptional({ nullable: true })
	displayName?: string | null;

	@EnumFieldOptional(() => Gender, { nullable: true })
	gender?: Gender | null;

	@StringFieldOptional({ nullable: true })
	avatarUrl?: string | null;

	@DateFieldOptional()
	dob?: Date | null;

	@StringFieldOptional({ nullable: true })
	address?: string | null;

	@StringFieldOptional({ nullable: true })
	identifierNumber?: string | null;

	constructor(userAccount: UserAccountEntity) {
		super(userAccount);

		// User Account data
		this.userId = userAccount.userId;
		this.username = userAccount.username ?? null;
		this.email = maskEmail(userAccount.email ?? '', 1, 3);
		this.phone = maskId(userAccount.phone ?? '', 0, 4);
		this.isEmailVerified = userAccount.isEmailVerified;
		this.isPhoneVerified = userAccount.isPhoneVerified;
		this.status = userAccount.status as UserAccountStatus;
		this.accountType = userAccount.accountType as UserAccountType;
		this.userBalance = userAccount.userBalance;

		// User Profile data (if exists)
		if (userAccount.userProfile) {
			this.profileId = userAccount.userProfile.profileId;
			this.displayName = userAccount.userProfile.displayName;
			this.gender = userAccount.userProfile.gender;
			this.avatarUrl = userAccount.userProfile.avatarUrl;
			this.dob = userAccount.userProfile.dob ? new Date(userAccount.userProfile.dob) : null;
			this.address = userAccount.userProfile.address;
			this.identifierNumber = maskId(userAccount.userProfile.identifierNumber ?? '', 0, 4);
		} else {
			// Default values if no profile exists
			this.profileId = 0;
			this.displayName = null;
			this.gender = null;
			this.avatarUrl = null;
			this.dob = null;
			this.address = null;
			this.identifierNumber = null;
		}
	}
}
