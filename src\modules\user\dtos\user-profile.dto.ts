import { AbstractDto } from '@common/dto/abstract.dto';
import { Gender } from '@constants/gender';
import {
	DateFieldOptional,
	EnumFieldOptional,
	NumberField,
	StringFieldOptional,
} from '@decorators/field.decorators';

import type { UserProfileEntity } from '../user-profile.entity';

export class UserProfileDto extends AbstractDto {
	@NumberField()
	profileId!: number;

	@NumberField()
	userId!: number;

	@StringFieldOptional({ nullable: true })
	displayName?: string | null;

	@EnumFieldOptional(() => Gender, { nullable: true })
	gender?: Gender | null;

	@StringFieldOptional({ nullable: true })
	avatarUrl?: string | null;

	@DateFieldOptional()
	dob?: Date | null;

	@StringFieldOptional({ nullable: true })
	address?: string | null;

	@StringFieldOptional({ nullable: true })
	identifierNumber?: string | null;

	constructor(userProfile: UserProfileEntity) {
		super(userProfile);
		this.profileId = userProfile.profileId;
		this.userId = userProfile.userId;
		this.displayName = userProfile.displayName;
		this.gender = userProfile.gender;
		this.avatarUrl = userProfile.avatarUrl;
		this.dob = userProfile.dob ? new Date(userProfile.dob) : null;
		this.address = userProfile.address;
		this.identifierNumber = userProfile.identifierNumber;
	}
}
