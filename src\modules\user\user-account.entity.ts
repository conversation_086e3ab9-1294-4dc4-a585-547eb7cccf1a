import { AbstractEntity } from '@common/abstract.entity';
import { UserAccountStatus, UserAccountType } from '@constants/user';
import { UseDto } from '@decorators/use-dto.decorator';
import { UserAccountDto } from '@modules/user/dtos/user-account.dto';
import {
	Column,
	Entity,
	Index,
	OneToMany,
	OneToOne,
	PrimaryGeneratedColumn,
} from 'typeorm';

import { AuditLogEntity } from '../audit-log/entities/audit-log.entity';
import { PaymentTransactionEntity } from '../payment/payment-transaction.entity.ts';
import { UserGameMappingEntity } from './user-game-mapping.entity.ts';
import { UserProfileEntity } from './user-profile.entity.ts';


@Index('user_account_username_key', ['username'], { unique: true })
@Index('user_account_email_key', ['email'], { unique: true })
@Index('user_account_pkey', ['userId'], { unique: true })
@Entity('user_account', { schema: 'public' })
@UseDto(UserAccountDto)
export class UserAccountEntity extends AbstractEntity<UserAccountDto> {
	@PrimaryGeneratedColumn({ type: 'bigint', name: 'user_id' })
	userId!: number;

	@Column('character varying', {
		name: 'username',
		unique: true,
		length: 100,
		nullable: true,
	})
	username?: string | null;

	@Column('character varying', {
		name: 'email',
		unique: true,
		length: 255,
		nullable: true,
	})
	email?: string | null;

	@Column('character varying', {
		name: 'phone',
		unique: true,
		length: 15,
		nullable: true,
	})
	phone?: string | null;

	@Column({ type: 'boolean', default: false })
	isEmailVerified!: boolean;

	@Column({ type: 'boolean', default: false })
	isPhoneVerified!: boolean;

	@Column('character varying', {
		name: 'password_hash',
		length: 255,
		nullable: true,
	})
	passwordHash?: string;

	@Column('character varying', {
		name: 'refresh_token',
		length: 1000,
		nullable: true,
	})
	refreshToken?: string | null;

	@Column('timestamp without time zone', {
		name: 'refresh_token_expires_at',
		nullable: true,
	})
	refreshTokenExpiresAt?: Date;

	@Column('character varying', {
		name: 'status',
		length: 30,
		default: UserAccountStatus.ACTIVE,
	})
	status!: UserAccountStatus;

	@Column('character varying', {
		name: 'account_type',
		length: 30,
		default: UserAccountType.QUICKPLAY,
	})
	accountType!: string;

	@Column('character varying', {
		name: 'social_uid',
		length: 255,
		nullable: true,
	})
	socialUid?: string | null;

	@Column('timestamp without time zone', {
		name: 'linked_at',
		nullable: true,
	})
	linkedAt?: Date | null;

	@Column('character varying', {
		name: 'social_access_token',
		length: 1000,
		nullable: true,
	})
	socialAccessToken?: string | null;

	@Column('character varying', {
		name: 'social_refresh_token',
		length: 1000,
		nullable: true,
	})
	socialRefreshToken?: string | null;

	@Column({
		type: 'bigint',
		name: 'user_balance',
		default: 0,
	})
	userBalance!: number;

	@Column('timestamp without time zone', {
		name: 'last_login_at',
		nullable: true,
	})
	lastLoginAt?: Date | null;

	@Column('character varying', {
		name: 'created_at_ip',
		nullable: true,
		length: 45,
	})
	createdAtIp!: string | null;

	@Column('character varying', {
		name: 'last_login_at_ip',
		nullable: true,
		length: 45,
	})
	lastLoginAtIp?: string | null;

	@OneToMany(() => AuditLogEntity, (auditLog) => auditLog.user)
	auditLogs!: AuditLogEntity[];

	@OneToMany(
		() => PaymentTransactionEntity,
		(paymentTransaction) => paymentTransaction.user,
		{ eager: true },
	)
	paymentTransactions!: PaymentTransactionEntity[];

	@OneToOne(() => UserProfileEntity, (userProfile) => userProfile.user, {
		eager: true,
		onDelete: 'CASCADE',
	})
	userProfile!: UserProfileEntity;

	@OneToMany(
		() => UserGameMappingEntity,
		(userGameMapping) => userGameMapping.user,
	)
	userGameMappings!: UserGameMappingEntity[];
}
