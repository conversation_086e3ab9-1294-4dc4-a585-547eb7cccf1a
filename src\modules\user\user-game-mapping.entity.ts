import { AbstractEntity } from '@common/abstract.entity';
import { UseDto } from '@decorators/use-dto.decorator';
import {
	Column,
	Entity,
	Index,
	JoinColumn,
	ManyToOne,
	PrimaryGeneratedColumn,
} from 'typeorm';

import { UserGameMappingDto } from './dtos/user-game-mapping.dto.ts';

@Index('idx_user_game_mapping', ['userId', 'gameId'], {})
@Index('user_game_mapping_pkey', ['ugmId'], { unique: true })
@Entity('user_game_mapping', { schema: 'public' })
@UseDto(UserGameMappingDto)
export class UserGameMappingEntity extends AbstractEntity<UserGameMappingDto> {
	@PrimaryGeneratedColumn({ type: 'bigint', name: 'ugm_id' })
	ugmId!: number;

	@Column('bigint', { name: 'user_id' })
	userId!: number;

	@Column('integer', { name: 'game_id' })
	gameId!: number;

	@Column('bigint', { name: 'game_balance', default: 0 })
	gameBalance!: number;

	@Column('timestamp without time zone', {
		name: 'assigned_at',
		default: () => 'CURRENT_TIMESTAMP',
	})
	assignedAt!: Date;

	@ManyToOne('UserAccountEntity', 'userGameMappings')
	@JoinColumn([{ name: 'user_id', referencedColumnName: 'userId' }])
	user!: 'UserAccountEntity';

	@ManyToOne('GameEntity', 'userGameMappings', { onDelete: 'CASCADE' })
	@JoinColumn([{ name: 'game_id', referencedColumnName: 'gameId' }])
	game!: 'GameEntity';
}
