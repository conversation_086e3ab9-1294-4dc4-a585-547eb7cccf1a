import { AbstractEntity } from '@common/abstract.entity';
import { Gender } from '@constants/gender.ts';
import { UseDto } from '@decorators/use-dto.decorator';
import {
	Column,
	Entity,
	Index,
	JoinColumn,
	OneToOne,
	PrimaryGeneratedColumn,
} from 'typeorm';

import { UserProfileDto } from './dtos/user-profile.dto.ts';

@Index('user_profile_pkey', ['profileId'], { unique: true })
@Index('user_profile_user_id_key', ['userId'], { unique: true })
@Entity('user_profile', { schema: 'public' })
@UseDto(UserProfileDto)
export class UserProfileEntity extends AbstractEntity<UserProfileDto> {
	@PrimaryGeneratedColumn({ type: 'bigint', name: 'profile_id' })
	profileId!: number;

	@Column('bigint', { name: 'user_id', unique: true })
	userId!: number;

	@Column('character varying', {
		name: 'display_name',
		length: 100,
		nullable: true,
		default: '',
	})
	displayName?: string | null;

	@Column({
		type: 'enum',
		enum: Gender,
		name: 'gender',
		nullable: true,
	})
	gender?: Gender | null;

	@Column('date', { name: 'dob', nullable: true })
	dob?: string | null;

	@Column('character varying', {
		name: 'avatar_url',
		nullable: true,
		length: 500,
	})
	avatarUrl?: string | null;

	@Column(
		'character varying',
		{ name: 'address', nullable: true, length: 500 },
	)
	address?: string | null;

	@Column(
		'character varying',
		{ name: 'identifier_number', nullable: true, length: 15 },
	)
	identifierNumber?: string | null;

	@Column({ type: 'date', nullable: true })
	id_issue_date?: Date;

	@Column('character varying', { nullable: true, length: 500 })
	id_front_url?: string;

	@Column('character varying', { nullable: true, length: 500 })
	id_back_url?: string;

	@OneToOne('UserAccountEntity', 'userProfile', { onDelete: 'CASCADE' })
	@JoinColumn([{ name: 'user_id', referencedColumnName: 'userId' }])
	declare user: 'UserAccountEntity';
}
