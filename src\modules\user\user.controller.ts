import { PageDto } from '@common/dto/page.dto';
import { getIp } from '@common/utils';
import { RoleType } from '@constants/role-type';
import { ApiCommonResponse } from '@decorators/api-common-response.decorator';
import { ApiPageResponse } from '@decorators/api-page-response.decorator';
import { AuthUser } from '@decorators/auth-user.decorator';
import { Auth } from '@decorators/http.decorators';
import { ContactVerifyDataDto } from '@modules/auth/dto/auth-response.dto';
import { ContactVerifyDto } from '@modules/auth/dto/contact-verify.dto';
import { SocialInfoDto } from '@modules/auth/dto/social-info.dto';
import { TransactionHistoryDto } from '@modules/payment/dtos/transaction-history.dto';
import { TransactionHistoryOptionsDto } from '@modules/payment/dtos/transaction-history-options.dto';
import {
	Body,
	Controller,
	Delete,
	Get,
	HttpCode,
	HttpStatus,
	Ip,
	Param,
	Post,
	Put,
	Query,
	Req,
	ValidationPipe,
	Version,
} from '@nestjs/common';
import { ApiBadRequestResponse, ApiBody, ApiOperation, ApiTags } from '@nestjs/swagger';
import type { Request } from 'express';

import { MutateUserProfileDto } from './dtos/mutate-user-profile.dto';
import { UserProfileResponseDto } from './dtos/user-profile-response.dto';
import { UserService } from './user.service';
import type { UserAccountEntity } from './user-account.entity';

@Controller('user')
@ApiTags('User')
export class UserController {
	constructor(private userService: UserService) {}

	@Post('verify-contact')
	@HttpCode(HttpStatus.OK)
	@Auth([RoleType.USER])
	@ApiOperation({
		summary: 'Verify contact for logged-in user',
		description:
		  'Check if email or phone number exists for a logged-in user. Sends OTP for verification.',
	})
	@ApiBody({ type: ContactVerifyDto })
	@ApiCommonResponse({
		type: ContactVerifyDataDto,
		description: 'Contact verification result',
	})
	@ApiBadRequestResponse({
		description: 'Invalid contact format or account issues',
	})
	async verifyContactForUser(
		@Body() contactVerifyDto: ContactVerifyDto,
		@Req() request: Request,
		@AuthUser() user: UserAccountEntity,
	): Promise<ContactVerifyDataDto> {
		return this.userService.verifyContactForUser(
			contactVerifyDto,
			getIp(request),
			user,
		);
	}

	@Version('1')
	@Get('profile')
	@HttpCode(HttpStatus.OK)
	@Auth([RoleType.USER])
	@ApiCommonResponse({
		type: UserProfileResponseDto,
		description: 'current user profile with profile data',
	})
	async getUserProfile(
		@AuthUser() user: UserAccountEntity,
	): Promise<UserProfileResponseDto> {
		// Load user with userProfile relation to get complete profile data
		const userWithProfile = await this.userService.findUserWithProfile(
			user.userId,
		);

		if (!userWithProfile) {
			// This should not happen since user is authenticated, but handle gracefully
			return new UserProfileResponseDto(user);
		}

		return new UserProfileResponseDto(userWithProfile);
	}

	@Version('1')
	@Put('profile')
	@HttpCode(HttpStatus.ACCEPTED)
	@Auth([RoleType.USER])
	@ApiBody({ type: MutateUserProfileDto })
	@ApiCommonResponse({
		status: HttpStatus.ACCEPTED,
		description: 'update user profile',
	})
	async updateUserProfile(
		@AuthUser() user: UserAccountEntity,
		@Body() mutateUserProfileDto: MutateUserProfileDto,
		@Ip() ip: string,
	): Promise<void> {
		return this.userService.updateUserProfile(user, mutateUserProfileDto, ip);
	}

	@Version('1')
	@Get('social/account')
	@HttpCode(HttpStatus.OK)
	@Auth([RoleType.USER])
	@ApiCommonResponse({
		type: SocialInfoDto,
		description: 'Get social account info',
	})
	async getSocialInfo(
		@AuthUser() user: UserAccountEntity,
	): Promise<SocialInfoDto> {
		return this.userService.getSocialInfo(user);
	}

	@Version('1')
	@Delete('social/account/:provider')
	@HttpCode(HttpStatus.OK)
	@Auth([RoleType.USER])
	@ApiCommonResponse({
		description: 'Unlink social account',
	})
	async unlinkSocialAccount(
		@AuthUser() user: UserAccountEntity,
		@Param('provider') provider: string,
	): Promise<void> {
		return this.userService.unlinkSocialAccount(user, provider);
	}

	@Version('1')
	@Get('transactions/history')
	@HttpCode(HttpStatus.OK)
	@Auth([RoleType.USER])
	@ApiPageResponse({
		type: TransactionHistoryDto,
		description: 'Get transaction history',
	})
	async getTransactionHistory(
		@AuthUser() user: UserAccountEntity,
		@Query(new ValidationPipe({ transform: true }))
		transactionHistoryOptionsDto: TransactionHistoryOptionsDto,
	): Promise<PageDto<TransactionHistoryDto>> {
		return this.userService.getTransactionHistory(
			user,
			transactionHistoryOptionsDto,
		);
	}
}