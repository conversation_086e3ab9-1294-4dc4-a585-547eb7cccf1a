import { HelpersModule } from '@common/helpers/helpers.module';
import { AuditLogModule } from '@modules/audit-log/audit-log.module';
import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SharedModule } from '@shared/shared.module';

import { GameEntity } from '../game/game.entity';
import { PaymentTransactionEntity } from '../payment/payment-transaction.entity';
import { CreateUserProfileHandler } from './commands/create-user-profile.command';
import { UserController } from './user.controller';
import { UserService } from './user.service';
import { UserAccountEntity } from './user-account.entity';
import { UserGameMappingEntity } from './user-game-mapping.entity';
import { UserProfileEntity } from './user-profile.entity';

const handlers = [CreateUserProfileHandler];

@Module({
	imports: [
		TypeOrmModule.forFeature([
			UserAccountEntity,
			UserProfileEntity,
			PaymentTransactionEntity,
			UserGameMappingEntity,
			GameEntity,
		]),
		SharedModule,
		HelpersModule,
		CqrsModule,
		AuditLogModule,
	],
	controllers: [UserController],
	exports: [UserService],
	providers: [UserService, ...handlers],
})
export class UserModule {}
