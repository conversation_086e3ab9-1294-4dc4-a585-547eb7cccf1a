// Common imports
import { PageDto } from '@common/dto/page.dto';
import { <PERSON>ache<PERSON>elper } from '@common/helpers/cache.helper';
import { ErrorHelper } from '@common/helpers/error.helper.ts';
import { isEmail } from '@common/utils.ts';
import { PaymentMethod, PaymentStatus } from '@constants/payment';
import { SocialCode, UserAccountType } from '@constants/user';
import type { ContactVerifyDataDto } from '@modules/auth/dto/auth-response.dto.ts';
import { SocialInfoDto } from '@modules/auth/dto/social-info.dto.ts';
import { VerifyContactEvent } from '@modules/auth/events/auth.events.ts';
import { GameEntity } from '@modules/game/game.entity.ts';
import { TransactionHistoryDto } from '@modules/payment/dtos/transaction-history.dto';
import type { TransactionHistoryOptionsDto } from '@modules/payment/dtos/transaction-history-options.dto';
import { PaymentTransactionEntity } from '@modules/payment/payment-transaction.entity';
import { Injectable } from '@nestjs/common';
import { CommandBus, EventBus } from '@nestjs/cqrs';
import { InjectRepository } from '@nestjs/typeorm';
import { OtpService } from '@shared/services/otp.service.ts';
import { RedisService } from '@shared/services/redis.service';
import { RedisKeyManagerService } from '@shared/services/redis-key-manager.service';
import { plainToClass } from 'class-transformer';
import {
	ContactConflictException,
	ContactRequiredException,
	InvalidUserTypeException,
	NotFoundException,
	UserNotFoundException,
	UserNotLinkedException,
} from 'exceptions';
import { GeneratorProvider } from 'providers/generator.provider.ts';
import type { FindManyOptions, FindOptionsWhere } from 'typeorm';
import { Repository } from 'typeorm';
import { Transactional } from 'typeorm-transactional';

import { ApiConfigService } from '../../shared/services/api-config.service.ts';
import { UserRegisterDto } from '../auth/dto/user-register.dto.ts';
import { UserRegisterSsoDto } from '../auth/dto/user-register-sso.dto.ts';
import { CreateUserProfileCommand } from './commands/create-user-profile.command.ts';
import { MutateUserProfileDto } from './dtos/mutate-user-profile.dto.ts';
import { UserProfileUpdatedEvent } from './events/user.events.ts';
import { UserProfileNotFoundException } from './exceptions/user-profile-not-found.exception.ts';
import { UserAccountEntity } from './user-account.entity.ts';
import { UserGameMappingEntity } from './user-game-mapping.entity.ts';
import { UserProfileEntity } from './user-profile.entity.ts';

@Injectable()
export class UserService {
	constructor(
		@InjectRepository(UserAccountEntity)
		private userRepository: Repository<UserAccountEntity>,
		@InjectRepository(UserProfileEntity)
		private userProfileRepository: Repository<UserProfileEntity>,
		@InjectRepository(PaymentTransactionEntity)
		private paymentTransactionRepository: Repository<PaymentTransactionEntity>,
		@InjectRepository(UserGameMappingEntity)
		private userGameMappingRepository: Repository<UserGameMappingEntity>,
		@InjectRepository(GameEntity)
		private gameRepository: Repository<GameEntity>,
		private commandBus: CommandBus,
		private eventBus: EventBus,
		private configService: ApiConfigService,
		private redisService: RedisService,
		private redisKeyManager: RedisKeyManagerService,
		private cacheHelper: CacheHelper,
		private otpService: OtpService,
	) {}

	findOne(
		findData: FindOptionsWhere<UserAccountEntity>,
	): Promise<UserAccountEntity | null> {
		return this.userRepository.findOneBy(findData);
	}

	find(findData: FindManyOptions<UserAccountEntity>): Promise<UserAccountEntity[]> {
		return this.userRepository.find(findData);
	}

	findByUsernameOrEmail(
		options: Partial<{ username?: string | null; email?: string | null }>,
	): Promise<UserAccountEntity | null> {
		const queryBuilder = this.userRepository.createQueryBuilder('user');

		if (options.email) {
			queryBuilder.orWhere('user.email = :email', {
				email: options.email,
			});
		}

		if (options.username) {
			queryBuilder.orWhere('user.username = :username', {
				username: options.username,
			});
		}

		return queryBuilder.getOne();
	}

	@Transactional()
	async createUser(
		userRegisterDto: UserRegisterDto,
		ip: string,
		isSdk: boolean = false,
		quickplayId: string | null = null,
	): Promise<UserAccountEntity> {
		const { identifier, password } = userRegisterDto;
		const user = this.userRepository.create();

		if (!identifier) {
			throw new ContactRequiredException('Identifier is required.');
		}

		user.username = quickplayId || `${SocialCode.get(
			UserAccountType.LOCAL,
		)}_${GeneratorProvider.uuidNoHyphens()}`;

		if (isEmail(identifier)) {
			user.email = identifier;
		} else {
			user.phone = identifier;
		}

		user.accountType = UserAccountType.LOCAL;
		user.passwordHash = password;
		user.createdAtIp = ip;

		// Set verification status based on registration method
		if (!isSdk) {
			if (isEmail(identifier)) {
				user.isEmailVerified = true; // Email was verified via OTP
				user.isPhoneVerified = false;
			} else {
				user.isEmailVerified = false;
				user.isPhoneVerified = true; // Phone was verified via OTP
			}
		}

		await this.userRepository.save(user);

		user.userProfile = await this.createUserProfile(
			user.userId,
			plainToClass(MutateUserProfileDto, {
				displayName: user.username,
				gender: null,
				avatarUrl: null,
				dob: null,
				address: null,
				identifierNumber: null,
			}),
		);

		return user;
	}

	@Transactional()
	async createUserSSO(
		socialInfo: SocialInfoDto,
		ip: string,
	): Promise<UserAccountEntity> {
		const userRegisterSsoDto = new UserRegisterSsoDto({
			username: `${SocialCode.get(
				socialInfo.provider,
			)}_${GeneratorProvider.uuidNoHyphens()}`,
			email: socialInfo.email || null,
			accountType: socialInfo.provider,
			socialUid: socialInfo.socialUid,
			linkedAt: new Date(),
			socialAccessToken: socialInfo.accessToken,
			socialRefreshToken: socialInfo.refreshToken,
			createdAtIp: ip,
		});

		const user = this.userRepository.create(userRegisterSsoDto);

		user.isEmailVerified = !!socialInfo.email;
		user.isPhoneVerified = false;

		await this.userRepository.save(user);

		user.userProfile = await this.createUserProfile(
			user.userId,
			plainToClass(MutateUserProfileDto, {
				displayName: socialInfo.name,
				gender: null,
				avatarUrl: socialInfo.avatarUrl,
				dob: null,
				address: null,
				identifierNumber: null,
			}),
		);

		return user;
	}

	@Transactional()
	async linkUserSSO(
		userAccount: UserAccountEntity,
		socialInfo: SocialInfoDto,
	): Promise<UserAccountEntity> {
		userAccount.socialUid = socialInfo.socialUid;
		userAccount.accountType = socialInfo.provider;
		userAccount.linkedAt = new Date();
		userAccount.socialAccessToken = socialInfo.accessToken ?? null;
		userAccount.socialRefreshToken = socialInfo.refreshToken ?? null;

		return await this.userRepository.save(userAccount);
	}

	async getSocialInfo(user: UserAccountEntity): Promise<SocialInfoDto> {
		const isSSOAccount =
      user.accountType === UserAccountType.FACEBOOK ||
      user.accountType === UserAccountType.GOOGLE ||
      user.accountType === UserAccountType.APPLE;

		if (!isSSOAccount) {
			throw new InvalidUserTypeException(
				'Tài khoản này không phải là tài khoản đăng nhập mạng xã hội.',
			);
		}

		if (!user.socialUid) {
			throw new UserNotFoundException(
				'Không tìm thấy UID mạng xã hội cho người dùng này.',
			);
		}

		return new SocialInfoDto({
			socialUid: user.socialUid,
			name: user.userProfile?.displayName || null,
			email: user.email || null,
			avatarUrl: user.userProfile?.avatarUrl || null,
			provider: user.accountType as UserAccountType,
		});
	}

	@Transactional()
	async unlinkSocialAccount(user: UserAccountEntity, provider: string): Promise<void> {
		const validProviders = [
			UserAccountType.FACEBOOK,
			UserAccountType.GOOGLE,
			UserAccountType.APPLE,
		];

		if (!validProviders.includes(user.accountType as UserAccountType)) {
			throw new UserNotLinkedException(
				'Tài khoản này không được liên kết với thông tin đăng nhập mạng xã hội.',
			);
		}

		if (user.accountType !== provider) {
			throw new UserNotLinkedException(
				`Tài khoản này không được liên kết với ${provider}.`,
			);
		}

		if (!user.socialUid) {
			throw new UserNotFoundException(
				'Hiện không có tài khoản xã hội nào được liên kết.',
			);
		}

		user.socialUid = null;
		user.socialAccessToken = null;
		user.socialRefreshToken = null;
		user.linkedAt = null;

		user.accountType = UserAccountType.LOCAL;

		await this.userRepository.save(user);
	}

	createUserProfile(
		userId: number,
		mutateUserProfileDto: MutateUserProfileDto,
	): Promise<UserProfileEntity> {
		return this.commandBus.execute<CreateUserProfileCommand, UserProfileEntity>(
			new CreateUserProfileCommand(userId, mutateUserProfileDto),
		);
	}

	async findUserWithProfile(userId: number): Promise<UserAccountEntity | null> {
		return this.userRepository.findOne({
			where: { userId },
			relations: ['userProfile'],
		});
	}

	async updateVerificationStatus(
		user: UserAccountEntity,
		verificationType: 'email' | 'phone',
	): Promise<void> {
		// Update verification status
		if (verificationType === 'email') {
			user.isEmailVerified = true;
		} else {
			user.isPhoneVerified = true;
		}

		await this.userRepository.save(user);
	}

	updateLastLoginInfo(
		userId: number,
		ip: string,
		socialAccessToken?: string | null,
	): Promise<UserAccountEntity> {
		return this.userRepository.save({
			userId,
			lastLoginAt: new Date(),
			lastLoginAtIp: ip,
			socialAccessToken,
		});
	}

	updateRefreshToken(
		userId: number,
		refreshToken?: string | null,
	): Promise<UserAccountEntity> {
		const refreshTokenExpiresAt = refreshToken
			? new Date(
				Date.now() +
            this.configService.authConfig.jwtRefreshTokenExpirationTime * 10,
			)
			: new Date(Date.now());

		return this.userRepository.save({
			userId,
			refreshToken,
			refreshTokenExpiresAt,
		});
	}

	async updateUser(
		user: UserAccountEntity,
		data: Partial<UserAccountEntity>,
	): Promise<UserAccountEntity> {
		if (!user) {
			throw new UserNotFoundException();
		}

		Object.assign(user, data);
		await this.userRepository.save(user);
		await this.invalidateUserCache(user.userId);

		return user;
	}

	async updateUserProfile(
		user: UserAccountEntity,
		mutateUserProfileDto: MutateUserProfileDto,
		ip: string,
	): Promise<void> {
		const userProfileEntity = await this.userProfileRepository.findOneBy({
			userId: user.userId,
		});

		if (!userProfileEntity) {
			throw new UserProfileNotFoundException();
		}

		// Get only the changed fields using generic method
		const { hasChanges, oldValues, newValues } = this.detectChanges(
			userProfileEntity,
			mutateUserProfileDto,
		);
	
		// If no fields changed, no need to update
		if (!hasChanges) {
			return;
		}

		// Apply changes to entity
		Object.assign(userProfileEntity, mutateUserProfileDto);

		await this.userProfileRepository.save(userProfileEntity);

		// Publish event with only changed fields
		this.eventBus.publish(
			new UserProfileUpdatedEvent(
				user.userId,
				ip,
				oldValues,
				newValues,
			),
		);

		await this.invalidateUserCache(user.userId);
	}

	/**
	 * Generic method to detect changes between entity and DTO
	 */
	private detectChanges<T extends Record<string, any>, U extends Record<string, any>>(
		entity: T,
		dto: U,
	): {
			hasChanges: boolean;
			oldValues: Partial<U>;
			newValues: Partial<U>;
		} {
		const oldValues: Partial<U> = {};
		const newValues: Partial<U> = {};

		// Field mapping for user profile
		const fieldMappings: Record<keyof U, keyof T> = {
			displayName: 'displayName',
			gender: 'gender',
			avatarUrl: 'avatarUrl',
			dob: 'dob',
			address: 'address',
			identifierNumber: 'identifierNumber',
			idIssueDate: 'id_issue_date',
			idFrontUrl: 'id_front_url',
			idBackUrl: 'id_back_url',
		} as Record<keyof U, keyof T>;

		Object.entries(dto).forEach(([dtoKey, dtoValue]) => {
			const entityKey = fieldMappings[dtoKey as keyof U];
		
			if (entityKey && dtoValue !== undefined) {
				const entityValue = entity[entityKey];
			
				// Normalize values for comparison
				const normalizedDtoValue = this.normalizeValue(dtoValue);
				const normalizedEntityValue = this.normalizeValue(entityValue);

				if (normalizedDtoValue !== normalizedEntityValue) {
					(oldValues as any)[dtoKey] = entityValue;
					(newValues as any)[dtoKey] = dtoValue;
				}
			}
		});

		return {
			hasChanges: Object.keys(oldValues).length > 0,
			oldValues,
			newValues,
		};
	}

	/**
	 * Normalize values for comparison (handle dates, nulls, etc.)
	 */
	private normalizeValue(value: any): any {
		if (value === null || value === undefined) {
			return value;
		}
	
		// Handle date comparison
		if (value instanceof Date) {
			return value.toISOString();
		}
	
		// Handle string dates
		if (typeof value === 'string' && /^\d{4}-\d{2}-\d{2}/.test(value)) {
			try {
				return new Date(value).toISOString();
			} catch {
				return value;
			}
		}
	
		return value;
	}

	async getTransactionHistory(
		user: UserAccountEntity,
		transactionHistoryOptionsDto: TransactionHistoryOptionsDto,
	): Promise<PageDto<TransactionHistoryDto>> {
		const queryBuilder = this.paymentTransactionRepository
			.createQueryBuilder('payment_transaction')
			.leftJoin('payment_transaction.user', 'user')
			.where('user.userId = :userId', { userId: user.userId })
			.orderBy('payment_transaction.createdAt', 'DESC');

		if (transactionHistoryOptionsDto.filterStatus) {
			queryBuilder.andWhere('payment_transaction.status = :status', {
				status: transactionHistoryOptionsDto.filterStatus,
			});
		}

		if (transactionHistoryOptionsDto.dateFrom) {
			queryBuilder.andWhere('payment_transaction.createdAt >= :dateFrom', {
				dateFrom: transactionHistoryOptionsDto.dateFrom,
			});
		}

		if (transactionHistoryOptionsDto.dateTo) {
			queryBuilder.andWhere('payment_transaction.createdAt <= :dateTo', {
				dateTo: transactionHistoryOptionsDto.dateTo,
			});
		}

		const [transactions, total] = await queryBuilder.getManyAndCount();

		const transactionDtos = transactions.map(
			(transaction) =>
				new TransactionHistoryDto({
					txId: transaction.txId,
					gameId: transaction.gameId,
					orderId: transaction.orderId,
					amount: transaction.amount,
					currency: transaction.currency,
					paymentMethod: transaction.paymentMethod as PaymentMethod,
					status: transaction.status as PaymentStatus,
					note: transaction.note,
					createdAt: transaction.createdAt,
				}),
		);

		const page = transactionHistoryOptionsDto.page || 1;
		const take = transactionHistoryOptionsDto.take || 10;
		const pageCount = Math.ceil(total / take);

		return new PageDto(transactionDtos, {
			page,
			take,
			itemCount: total,
			pageCount,
			hasPreviousPage: page > 1,
			hasNextPage: page < pageCount,
		});
	}

	async invalidateUserCache(userId: number): Promise<void> {
		const cacheKey = this.redisKeyManager.user.cache(userId.toString());
		await this.redisService.del(cacheKey);
	}

	async cacheUserProfile(
		userId: number,
		profileData: UserAccountEntity,
	): Promise<void> {
		const key = this.redisKeyManager.user.cache(userId.toString());
		await this.cacheHelper.setWithErrorHandling(key, profileData, 3600, 'user');
	}

	async getCachedUserProfile(userId: number): Promise<any | null> {
		const key = this.redisKeyManager.user.cache(userId.toString());
		try {
			return await this.redisService.get(key);
		} catch (error) {
			console.warn(`Failed to get cached user profile for ${userId}:`, error);
			return null;
		}
	}

	async updatePassword(userId: number, newPassword: string): Promise<void> {
		const user = await this.userRepository.findOne({ where: { userId } });
		if (!user) {
			throw new UserNotFoundException();
		}

		user.passwordHash = newPassword;
		user.updatedAt = new Date();

		await this.userRepository.save(user);

		await this.invalidateUserCache(userId);
	}

	async addUserGameMapping(
		userId: number,
		gameKey: string,
	): Promise<UserGameMappingEntity> {
		const game = await this.gameRepository.findOne({ where: { gameKey } });
		if (!game) {
			throw new NotFoundException('Game not found');
		}

		const existing = await this.userGameMappingRepository.findOne({
			where: { userId: userId, gameId: game.gameId },
		});
		if (existing) return existing;

		// Tạo mới mapping
		const mapping = this.userGameMappingRepository.create({
			userId,
			gameId: game.gameId,
		});

		return await this.userGameMappingRepository.save(mapping);
	}

	async findExistingUser(
		contact: string,
	): Promise<{
			existingUser: UserAccountEntity | null;
			contactType: 'email' | 'phone'
		}> {
		const contactType = ErrorHelper.validateContactFormat(contact);
		// Check user exist
		const existingUser = await this.findOne({ [contactType]: contact });
		return { existingUser: existingUser ?? null, contactType };
	}

	async verifyContactForUser<T extends { contact: string }>(
		dto: T,
		ip: string,
		currentUser: UserAccountEntity,
	): Promise<ContactVerifyDataDto> {
		const { contact } = dto;
		if (!contact) {
			throw new ContactRequiredException('Contact is required.');
		}

		const { existingUser, contactType } = await this.findExistingUser(contact);

		if (existingUser && existingUser.userId !== currentUser.userId) {
			// Log failed contact verification due to conflict
			this.eventBus.publish(
				new VerifyContactEvent(
					currentUser.userId,
					contact,
					'contact_conflict',
				),
			);
			
			throw new ContactConflictException(
				`This ${contactType} is already in use by another account.`,
			);
		}

		const isVerified = contactType === 'email'
			? currentUser.isEmailVerified
			: currentUser.isPhoneVerified;

		if (existingUser && existingUser.userId === currentUser.userId && isVerified) {
			// Log successful contact verification (already verified)
			this.eventBus.publish(
				new VerifyContactEvent(
					currentUser.userId,
					contact,
					'already_verified',
				),
			);
			
			return {
				status: 'ALREADY_VERIFIED',
				contactType,
				message: `This ${contactType} is already associated with your account and verified.`,
				details: {
					userExists: true,
					accountStatus: currentUser.status,
				},
			};
		}

		await this.updateUser(currentUser, { [contactType]: contact });

		await this.otpService.sendOtp(contact, ip);
		return {
			status: 'OTP_SENT',
			contactType,
			message: `OTP sent to your new ${contactType} for verification.`,
			details: {
				userExists: false,
			},
		};
	}
}
