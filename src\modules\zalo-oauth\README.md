# Zalo OAuth Module

## 📋 Overview

The Zalo OAuth module provides integration with Zalo's OAuth 2.0 authentication system, enabling users to authenticate using their Zalo accounts. This module handles the complete OAuth flow, token management, and user information retrieval from Zalo's API.

## 🔧 Features

### OAuth 2.0 Integration
- **Authorization Flow** - Complete OAuth 2.0 PKCE flow
- **Token Management** - Access and refresh token handling
- **User Information** - Retrieve user profile from Zalo
- **State Validation** - CSRF protection with state parameter

### Security Features
- **PKCE Support** - Proof Key for Code Exchange
- **State Parameter** - CSRF attack prevention
- **Token Validation** - Secure token verification
- **Dynamic Configuration** - Configurable OAuth endpoints

### Zalo Integration
- **Official API** - Uses Zalo's official OAuth API
- **User Profile** - Access to Zalo user information
- **Vietnamese Market** - Optimized for Vietnamese users
- **Mobile Support** - Works with Zalo mobile app

## 🚀 API Endpoints

### OAuth Flow (Admin Only)
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| `GET` | `/zalo/authorize` | Initiate Zalo OAuth authorization flow | ✅ Admin |
| `GET` | `/zalo/callback` | Handle OAuth callback from Zalo | ❌ |

### Management & Configuration (Admin Only)
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| `GET` | `/zalo/status` | Check OAuth connection status | ✅ Admin |
| `GET` | `/zalo/refresh` | Force refresh Zalo access token | ✅ Admin |
| `GET` | `/zalo/urls` | Get OAuth URLs configuration | ✅ Admin |
| `POST` | `/zalo/urls/reset` | Reset URLs to default values | ✅ Admin |

## 📊 Request/Response Examples

### 🔐 Start Authorization Flow
**Request:**
```http
GET /zalo/authorize
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (302 Redirect):**
Redirects to Zalo authorization URL:
```
https://oauth.zaloapp.com/v4/oa/permission?app_id=123&redirect_uri=callback&state=abc&code_challenge=xyz&code_challenge_method=S256
```

### 📞 OAuth Callback
**Request:**
```http
GET /zalo/callback?code=auth_code&state=abc&oa_id=123
```

**Response (200 OK):**
```json
{
  "message": "OK"
}
```

**Response (400 Error):**
```json
{
  "error": "callback_error",
  "message": "Authorization code not provided"
}
```

### 📊 Check Connection Status
**Request:**
```http
GET /zalo/status
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 OK):**
```json
{
  "connected": true,
  "hasToken": true,
  "tokenPrefix": "eyJhbGciOiJIUzI1NiIs...",
  "configured": true,
  "tokenExpired": false,
  "expiresAt": "2025-08-16T11:30:00Z"
}
```

### 🔄 Refresh Token
**Request:**
```http
GET /zalo/refresh
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Token refreshed successfully",
  "expires_in": 3600
}
```

### ⚙️ Get URLs Configuration
**Request:**
```http
GET /zalo/urls
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 OK):**
```json
{
  "oauthUrl": "https://oauth.zaloapp.com/v4/oa/permission",
  "tokenUrl": "https://oauth.zaloapp.com/v4/oa/access_token",
  "isDefault": {
    "oauthUrl": true,
    "tokenUrl": true
  }
}
```

### 🔄 Reset URLs to Default
**Request:**
```http
POST /zalo/urls/reset
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Zalo URLs reset to default values"
}
```

## 🔧 Configuration

### Environment Variables
```bash
ZALO_APP_ID=your_zalo_app_id
ZALO_APP_SECRET=your_zalo_app_secret
BACKEND_URL=https://your-backend.com
```

### Dynamic Configuration (via sys-config)
```json
{
  "ZALO_OA_URL": "https://oauth.zaloapp.com/v4/oa/permission",
  "ZALO_TOKEN_URL": "https://oauth.zaloapp.com/v4/oa/access_token",
  "zalo_callback_timeout": 30
}
```

## 📈 Usage Examples

### Complete OAuth Flow
```typescript
// 1. Get authorization URL
const authResponse = await fetch('/zalo/auth');
const { authorizationUrl } = await authResponse.json();

// 2. Redirect user to Zalo
window.location.href = authorizationUrl;

// 3. Handle callback (automatic)
// User is redirected to /zalo/callback with code and state

// 4. Exchange code for tokens (handled by backend)
// Backend automatically processes the callback

// 5. Get user profile
const profileResponse = await fetch('/zalo/profile', {
  headers: {
    'Authorization': `Bearer ${zaloAccessToken}`
  }
});
const userProfile = await profileResponse.json();
```

### Integration with Auth Module
```typescript
// After successful Zalo OAuth, integrate with main auth system
const socialInfo = {
  provider: 'zalo',
  socialUid: userProfile.id,
  name: userProfile.name,
  email: userProfile.email, // if available
  avatarUrl: userProfile.picture?.data?.url,
  accessToken: zaloAccessToken,
  refreshToken: zaloRefreshToken
};

// Create or login user via AuthService
const authResult = await authService.socialLogin(socialInfo, userIp);
```

## 🔒 Security Implementation

### PKCE (Proof Key for Code Exchange)
```typescript
// Generate code verifier and challenge
const codeVerifier = GeneratorProvider.uuid();
const codeChallenge = GeneratorProvider.zaloCodeChallenge(codeVerifier);

// Store verifier securely
await this.setZaloConfig('ZALO_CODE_VERIFIER', codeVerifier);

// Use challenge in authorization URL
const authUrl = `${baseUrl}?code_challenge=${codeChallenge}&code_challenge_method=S256`;
```

### State Parameter Validation
```typescript
// Generate and store state
const state = GeneratorProvider.generateRandomString(5);
await this.setZaloConfig('ZALO_STATE', state);

// Validate state in callback
const storedState = await this.getZaloConfig('ZALO_STATE');
if (callbackState !== storedState) {
  throw new BadRequestException('Invalid state parameter');
}
```

### Token Security
- **Secure Storage** - Tokens stored securely in sys-config
- **Expiration Handling** - Automatic token expiration management
- **Refresh Logic** - Automatic token refresh when needed
- **Scope Validation** - Validate token scopes and permissions

## 📊 Data Models

### Zalo Token Response
```typescript
{
  access_token: string,        // Zalo access token
  refresh_token: string,       // Zalo refresh token
  expires_in: number,          // Token expiration time (seconds)
  token_type: string,          // Token type (Bearer)
  scope: string               // Granted permissions
}
```

### Zalo User Profile
```typescript
{
  id: string,                  // Zalo user ID
  name: string,                // User display name
  picture: {
    data: {
      url: string              // Profile picture URL
    }
  },
  email?: string,              // Email (if granted)
  birthday?: string,           // Birthday (if granted)
  gender?: string             // Gender (if granted)
}
```

## 🔧 Configuration Management

### Dynamic URLs
```typescript
// Get configurable OAuth URLs
const oauthUrl = await this.getZaloConfig('ZALO_OA_URL', this.DEFAULT_ZALO_OA_URL);
const tokenUrl = await this.getZaloConfig('ZALO_TOKEN_URL', this.DEFAULT_ZALO_TOKEN_URL);
```

### Timeout Configuration
```typescript
// Configurable callback timeout
const callbackTimeout = await this.getZaloConfig('zalo_callback_timeout', 30);
```

## 🚨 Error Handling

### HTTP Status Codes
| Status Code | Description | Common Scenarios |
|-------------|-------------|------------------|
| `400` | Bad Request | Invalid authorization code, callback errors |
| `401` | Unauthorized | Missing or invalid admin token |
| `403` | Forbidden | Non-admin user accessing admin endpoints |
| `500` | Internal Server Error | Zalo API errors, configuration issues |

### Error Response Format
All error responses follow this structure:
```json
{
  "statusCode": 400,
  "message": "Authorization code not provided",
  "error": "Bad Request",
  "timestamp": "2025-08-16T10:30:00Z",
  "path": "/zalo/callback"
}
```

### Common Error Examples

**Authorization Required (401):**
```json
{
  "statusCode": 401,
  "message": "Unauthorized",
  "error": "Unauthorized"
}
```

**Admin Access Required (403):**
```json
{
  "statusCode": 403,
  "message": "Forbidden resource",
  "error": "Forbidden"
}
```

**Callback Error (400):**
```json
{
  "error": "callback_error",
  "message": "Authorization code not provided"
}
```

**Token Refresh Error (400):**
```json
{
  "statusCode": 400,
  "message": "Failed to refresh token: Invalid refresh token",
  "error": "Bad Request"
}
```

## 📱 Mobile Integration

### Zalo App Integration
- **Deep Links** - Direct integration with Zalo mobile app
- **Fallback** - Web-based OAuth for non-app users
- **Universal Links** - iOS universal link support
- **Intent Filters** - Android intent filter support

### Mobile OAuth Flow
```typescript
// Check if Zalo app is installed
const hasZaloApp = await checkZaloAppInstalled();

if (hasZaloApp) {
  // Use deep link for app-to-app OAuth
  const deepLink = `zalo://oauth?app_id=${appId}&redirect_uri=${callbackUri}`;
  window.location.href = deepLink;
} else {
  // Use web-based OAuth
  window.location.href = webOAuthUrl;
}
```

## 🔧 Development Setup

### Zalo App Configuration
1. **Register App** - Register your app on Zalo Developer Portal
2. **Configure Domains** - Add your domain to allowed domains
3. **Set Redirect URIs** - Configure OAuth redirect URIs
4. **Get Credentials** - Obtain App ID and App Secret

### Testing
```bash
# Test authorization URL generation
GET /zalo/auth

# Test callback handling (use actual Zalo response)
GET /zalo/callback?code=test_code&state=test_state

# Test token exchange
POST /zalo/token
{
  "code": "test_authorization_code",
  "state": "test_state"
}
```

## 📝 Implementation Notes

### Security & Access Control
- **Admin Only Access**: All endpoints require admin authentication except callback
- **PKCE Implementation**: Proof Key for Code Exchange for enhanced security
- **State Validation**: CSRF protection with state parameter verification
- **Token Security**: Secure storage in sys-config with expiration handling

### Integration Flow
1. **Admin Authorization**: Admin initiates OAuth flow via `/zalo/authorize`
2. **Zalo Redirect**: User redirected to Zalo for authorization
3. **Callback Handling**: Zalo redirects to `/zalo/callback` with auth code
4. **Token Exchange**: Backend exchanges code for access/refresh tokens
5. **Token Storage**: Tokens stored securely in sys-config for later use

### Configuration Management
- **Dynamic URLs**: OAuth and token URLs configurable via sys-config
- **Default Fallback**: Automatic fallback to default Zalo endpoints
- **Environment Variables**: App ID and secret from environment
- **Admin Controls**: Admin can check status, refresh tokens, reset config

### Usage Scenarios
- **Social Login Integration**: Use tokens for Zalo user authentication
- **API Access**: Access Zalo APIs on behalf of authorized users
- **Token Management**: Automatic refresh and expiration handling
- **Monitoring**: Connection status and health checks for admins

## 🔗 Related Modules

- [Auth Module](../auth/README.md) - Main authentication system
- [User Module](../user/README.md) - User account management
- [Sys-Config Module](../sys-config/README.md) - Dynamic configuration
