// 2. DTO cho Zalo OAuth
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsNumber, IsOptional, IsString } from 'class-validator';

export class ZaloTokenResponseDto {
	@ApiPropertyOptional({
		example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
		description: 'Zalo access token',
	})
	@IsOptional()
	@IsString()
	access_token?: string;

	@ApiPropertyOptional({
		example: 'refresh_token_example',
		description: 'Zalo refresh token',
	})
	@IsOptional()
	@IsString()
	refresh_token?: string;

	@ApiPropertyOptional({
		example: 3600,
		description: 'Token expiration time in seconds',
	})
	@IsOptional()
	@IsNumber()
	expires_in?: number;

	@ApiPropertyOptional({
		example: 'publish_feed,manage_page',
		description: 'Granted permissions scope',
	})
	@IsOptional()
	@IsString()
	scope?: string;
}

export class ZaloCallbackDto {
	@IsOptional()
	@IsString()
	code?: string;

	@IsOptional()
	@IsString()
	state?: string;

	@IsOptional()
	@IsString()
	oa_id?: string;

	@IsOptional()
	@IsString()
	code_challenge?: string;
}

export class ConnectionStatusDto {
	@ApiProperty()
	@IsBoolean()
	connected?: boolean | false;

	@ApiProperty()
	@IsBoolean()
	hasToken!: boolean | false;

	@ApiProperty()
	@IsBoolean()
	configured!: boolean | false;

	@ApiProperty()
	@IsBoolean()
	tokenExpired!: boolean | false;

	@ApiPropertyOptional()
	@IsString()
	@IsOptional()
	tokenPrefix?: string | null;

	@ApiPropertyOptional()
	@IsString()
	@IsOptional()
	expiresAt?: string | null;

	@ApiPropertyOptional()
	@IsString()
	@IsOptional()
	error?: string;
}

export class ZaloUrlsDto {
	@ApiProperty()
	@IsString()
	oauthUrl!: string;

	@ApiProperty()
	@IsString()
	tokenUrl!: string;

	@ApiProperty({ type: 'object', properties: { oauthUrl: { type: 'boolean' }, tokenUrl: { type: 'boolean' } } })
	isDefault!: {
		oauthUrl: boolean;
		tokenUrl: boolean;
	};
}