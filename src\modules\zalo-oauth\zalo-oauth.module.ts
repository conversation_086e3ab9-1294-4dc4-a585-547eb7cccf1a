// 5. Module configuration
import { SysConfigModule } from '@modules/sys-config/sys-config.module';
import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

import { ZaloOAuthController } from './zalo-oauth.controller';
import { ZaloOAuthService } from './zalo-oauth.service';

@Module({
	imports: [
		HttpModule,
		ConfigModule,
		SysConfigModule, // Import SysConfigModule instead of Entity
	],
	controllers: [ZaloOAuthController],
	providers: [ZaloOAuthService],
	exports: [ZaloOAuthService],
})
export class ZaloOAuthModule {}
