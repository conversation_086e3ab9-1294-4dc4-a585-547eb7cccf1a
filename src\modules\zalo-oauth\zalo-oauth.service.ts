import { SysConfigService } from '@modules/sys-config/sys-config.service';
import { HttpService } from '@nestjs/axios';
import { Injectable, Logger } from '@nestjs/common';
import type { AxiosResponse } from 'axios';
import {
	InvalidUrlException,
	ZaloGetExchangeTokenFailedException,
	ZaloMissingAccessTokenException,
	ZaloMissingRefreshTokenException,
	ZaloOAuthFailedException,
} from 'exceptions';
import { GeneratorProvider } from 'providers/generator.provider';
import { firstValueFrom } from 'rxjs';

import {
	ConnectionStatusDto,
	ZaloTokenResponseDto,
	ZaloUrlsDto,
} from './dto/zalo-oauth.dto';
import { ApiConfigService } from '@shared/services/api-config.service';

@Injectable()
export class ZaloOAuthService {
	private readonly logger = new Logger(ZaloOAuthService.name);

	private readonly DEFAULT_ZALO_OA_URL =
		'https://oauth.zaloapp.com/v4/oa/permission';
	private readonly DEFAULT_ZALO_TOKEN_URL =
		'https://oauth.zaloapp.com/v4/oa/access_token';

	constructor(
		private sysConfigService: SysConfigService,
		private configService: ApiConfigService,
		private httpService: HttpService,
	) {}

	async getAuthorizationUrl(): Promise<string> {
		const state = GeneratorProvider.generateRandomString(5);
		const codeVerifier = GeneratorProvider.uuid();
		const codeChallenge = GeneratorProvider.zaloCodeChallenge(codeVerifier);

		await this.setZaloConfig(
			'ZALO_CODE_VERIFIER',
			codeVerifier,
			'Temporary OAuth code verifier',
		);
		await this.setZaloConfig(
			'ZALO_STATE',
			state,
			'Temporary OAuth state parameter',
		);

		const params = new URLSearchParams(<Record<string, string>>{
			app_id: this.configService.zaloCert.clientID,
			redirect_uri: `${this.configService.backendUrl}/zalo/callback`,
			state: state,
			code_challenge: codeChallenge,
		});

		const oauthUrl = await this.getZaloOAuthUrl();
		return `${oauthUrl}?${params.toString()}`;
	}

	async zaloTokenRequest(
		params: Record<string, unknown>,
	): Promise<ZaloTokenResponseDto> {
		try {
			const fullParams = {
				...params,
				app_id: this.configService.zaloCert.clientID,
				code_verifier: await this.getZaloConfig('ZALO_CODE_VERIFIER'),
			};
			const tokenUrl = await this.getZaloTokenUrl();
			const response: AxiosResponse<ZaloTokenResponseDto> =
        await firstValueFrom(
        	this.httpService.post<ZaloTokenResponseDto>(tokenUrl, fullParams, {
        		headers: {
        			'Content-Type': 'application/x-www-form-urlencoded',
        			secret_key: this.configService.zaloCert.clientSecret,
        		},
        	}),
        );

			const tokenData = response.data;
			if (!tokenData) {
				throw new ZaloOAuthFailedException('Zalo OAuth error');
			}

			await this.saveTokensToConfig(tokenData);

			return tokenData;
		} catch (error) {
			if (error instanceof Error) {
				this.logger.error(
					'Error exchanging code for tokens',
					(error as any).response?.data || error.message,
				);
			}
			throw new ZaloGetExchangeTokenFailedException('Failed to exchange code for tokens');
		}
	}

	async exchangeCodeForTokens(code: string): Promise<ZaloTokenResponseDto> {
		return await this.zaloTokenRequest({
			grant_type: 'authorization_code',
			code,
		});
	}

	async refreshAccessToken(): Promise<ZaloTokenResponseDto> {
		const refreshToken = await this.getZaloConfig('ZALO_REFRESH_TOKEN');
		if (!refreshToken) {
			throw new ZaloMissingRefreshTokenException('No refresh token available');
		}

		return await this.zaloTokenRequest({
			grant_type: 'refresh_token',
			refresh_token: refreshToken,
		});
	}

	private async saveTokensToConfig(
		tokenData: ZaloTokenResponseDto,
	): Promise<void> {
		if (tokenData.access_token) {
			await this.setZaloConfig(
				'ZALO_ACCESS_TOKEN',
				tokenData.access_token,
				'Zalo Official Account Access Token',
			);
		}

		if (tokenData.refresh_token) {
			await this.setZaloConfig(
				'ZALO_REFRESH_TOKEN',
				tokenData.refresh_token,
				'Zalo Official Account Refresh Token',
			);
		}

		const expiresAt = new Date(Date.now() + (tokenData.expires_in ?? 0) * 1000);
		await this.setZaloConfig(
			'ZALO_TOKEN_EXPIRES_AT',
			expiresAt.toISOString(),
			'Zalo token expiration timestamp',
		);

		this.logger.log('Zalo tokens saved successfully');
	}

	private async getZaloConfig(
		key: string,
		fallback: string = '',
	): Promise<string> {
		try {
			return await this.sysConfigService.getValue<string>(key);
		} catch (_error) {
			this.logger.warn(
				`Zalo config "${key}" not found, using fallback: ${fallback}`,
				_error,
			);
			return fallback;
		}
	}

	private async getZaloOAuthUrl(): Promise<string> {
		return await this.getZaloConfig('ZALO_OA_URL', this.DEFAULT_ZALO_OA_URL);
	}

	private async getZaloTokenUrl(): Promise<string> {
		return await this.getZaloConfig(
			'ZALO_TOKEN_URL',
			this.DEFAULT_ZALO_TOKEN_URL,
		);
	}

	private async setZaloConfig(
		key: string,
		value: string,
		description: string,
	): Promise<void> {
		await this.sysConfigService.setValue({
			key,
			value,
			description,
			dataType: 'string',
			category: 'zalo',
		});
	}

	async isConfigured(): Promise<boolean> {
		const appId = this.configService.zaloCert.clientID;
		const appSecret = this.configService.zaloCert.clientSecret;
		const oauthUrl = await this.getZaloConfig('ZALO_OA_URL');
		const tokenUrl = await this.getZaloConfig('ZALO_TOKEN_URL');

		return !!(appId && appSecret && oauthUrl && tokenUrl);
	}

	async getOAuthStatus(): Promise<ConnectionStatusDto> {
		const configured = await this.isConfigured();
		const accessToken = await this.getZaloConfig('ZALO_ACCESS_TOKEN');
		const expiresAt = await this.getZaloConfig('ZALO_TOKEN_EXPIRES_AT');

		let tokenExpired = true;
		if (expiresAt) {
			tokenExpired = new Date() >= new Date(expiresAt);
		}

		return {
			configured,
			hasToken: !!accessToken,
			tokenExpired,
			expiresAt: expiresAt || undefined,
		};
	}

	async clearOAuthConfig(): Promise<void> {
		const keysToDelete = [
			'ZALO_ACCESS_TOKEN',
			'ZALO_REFRESH_TOKEN',
			'ZALO_TOKEN_EXPIRES_AT',
			'ZALO_CODE_VERIFIER',
			'ZALO_STATE',
		];

		for (const key of keysToDelete) {
			try {
				await this.sysConfigService.deleteConfig(key);
			} catch (error) {
				this.logger.warn(
					`Failed to delete config "${key}": ${error instanceof Error ? error.message : 'Unknown error'}`,
				);
			}
		}

		this.logger.log('Zalo OAuth configuration cleared');
	}

	async updateZaloUrls(oauthUrl?: string, tokenUrl?: string): Promise<void> {
		if (oauthUrl) {
			if (!oauthUrl.startsWith('https://')) {
				throw new InvalidUrlException('OAuth URL must use HTTPS protocol');
			}

			await this.setZaloConfig(
				'ZALO_OA_URL',
				oauthUrl,
				'Zalo OAuth authorization URL',
			);
			this.logger.log(`Zalo OAuth URL updated to: ${oauthUrl}`);
		}

		if (tokenUrl) {
			if (!tokenUrl.startsWith('https://')) {
				throw new InvalidUrlException('Token URL must use HTTPS protocol');
			}

			await this.setZaloConfig(
				'ZALO_TOKEN_URL',
				tokenUrl,
				'Zalo OAuth token exchange URL',
			);
			this.logger.log(`Zalo Token URL updated to: ${tokenUrl}`);
		}
	}

	async getZaloUrls(): Promise<ZaloUrlsDto> {
		const oauthUrl = await this.getZaloOAuthUrl();
		const tokenUrl = await this.getZaloTokenUrl();

		return {
			oauthUrl,
			tokenUrl,
			isDefault: {
				oauthUrl: oauthUrl === this.DEFAULT_ZALO_OA_URL,
				tokenUrl: tokenUrl === this.DEFAULT_ZALO_TOKEN_URL,
			},
		};
	}

	async getValidAccessToken(): Promise<string> {
		const accessToken = await this.getZaloConfig('ZALO_ACCESS_TOKEN');
		const expiresAt = await this.getZaloConfig('ZALO_TOKEN_EXPIRES_AT');

		if (!accessToken) {
			throw new ZaloMissingAccessTokenException(
				'No access token available. Please authorize first.',
			);
		}

		if (expiresAt) {
			const expireTime = new Date(expiresAt);
			const now = new Date();
			const fiveMinutesFromNow = new Date(now.getTime() + 5 * 60 * 1000);

			if (expireTime <= fiveMinutesFromNow) {
				this.logger.log('Access token will expire soon, refreshing...');
				const newTokens = await this.refreshAccessToken();
				return newTokens.access_token ?? '';
			}
		}

		return accessToken;
	}

	async resetZaloUrlsToDefault(): Promise<void> {
		await this.setZaloConfig(
			'ZALO_OA_URL',
			this.DEFAULT_ZALO_OA_URL,
			'Zalo OAuth authorization URL',
		);

		await this.setZaloConfig(
			'ZALO_TOKEN_URL',
			this.DEFAULT_ZALO_TOKEN_URL,
			'Zalo OAuth token exchange URL',
		);

		this.logger.log('Zalo URLs reset to default values');
	}
}