import { createHash, randomInt } from 'crypto';
import { v1 as uuid } from 'uuid';

export class GeneratorProvider {
	static uuid(): string {
		return uuid();
	}

	static uuidNoHyphens(): string {
		return uuid().replace(/-/g, '');
	}

	static fileName(ext: string): string {
		return `${GeneratorProvider.uuid()}.${ext}`;
	}

	static generateVerificationCode(): string {
		return Math.floor(1000 + Math.random() * 9000).toString();
	}

	static generatePassword(): string {
		const lowercase = 'abcdefghijklmnopqrstuvwxyz';
		const uppercase = lowercase.toUpperCase();
		const numbers = '**********';

		let text = '';

		for (let i = 0; i < 4; i++) {
			text += uppercase.charAt(Math.floor(Math.random() * uppercase.length));
			text += lowercase.charAt(Math.floor(Math.random() * lowercase.length));
			text += numbers.charAt(Math.floor(Math.random() * numbers.length));
		}

		return text;
	}

	/**
	 * generate random string
	 * @param length
	 */
	static generateRandomString(length: number): string {
		return Math.random()
			.toString(36)
			.replaceAll(/[^\dA-Za-z]+/g, '')
			.slice(0, Math.max(0, length));
	}

	static zaloCodeChallenge(codeVerifier: string): string {
		const digest = createHash('sha256').update(codeVerifier, 'ascii').digest();
		return digest
			.toString('base64')
			.replace(/\+/g, '-')
			.replace(/\//g, '_')
			.replace(/=+$/, '');
	}
	
	static generateOtp(): string {
		return randomInt(100000, 999999).toString();
	}
}
