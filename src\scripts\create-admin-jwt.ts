#!/usr/bin/env tsx

import { JwtService } from '@nestjs/jwt';
import * as dotenv from 'dotenv';
import * as path from 'path';

import { RoleType } from '../constants/role-type';
import { TokenType } from '../constants/token-type';

// Load environment variables from .env file
dotenv.config({ path: path.resolve(process.cwd(), '.env') });

async function createAdminJwt() {
	console.info('🚀 Starting admin JWT creation...\n');

	const rawPrivateKey = process.env.JWT_PRIVATE_KEY;
	const rawPublicKey = process.env.JWT_PUBLIC_KEY;

	if (!rawPrivateKey || !rawPublicKey) {
		console.error('❌ Missing JWT_PRIVATE_KEY or JWT_PUBLIC_KEY in environment');
		process.exit(1);
	}

	// Handle escaped newlines (e.g., from .env)
	const privateKey = rawPrivateKey.replace(/\\n/g, '\n');
	const publicKey = rawPublicKey.replace(/\\n/g, '\n');

	const jwtService = new JwtService({
		privateKey,
		publicKey,
		signOptions: {
			algorithm: 'RS256',
		},
		verifyOptions: {
			algorithms: ['RS256'],
		},
	});

	try {
		const token = await jwtService.signAsync(
			{
				userId: 1,
				type: TokenType.ACCESS_TOKEN,
				role: RoleType.ADMIN,
			},
			{
				expiresIn: '1h',
			},
		);

		console.info('✅ Admin ACCESS_TOKEN created successfully!');
		console.info('🔐 Algorithm: RS256');
		console.info('⏲️  Expires In: 1h\n');
		console.info('🪪 JWT:');
		console.info(token);

		// Optional quick verification to ensure token is valid with provided public key
		const payload = await jwtService.verifyAsync(token).catch(() => null);
		if (payload) {
			console.info('\n📦 Payload preview:', {
				userId: payload.userId,
				type: payload.type,
				role: payload.role,
				exp: payload.exp,
				iat: payload.iat,
			});
		}
	} catch (error) {
		console.error(
			'❌ Failed to create admin JWT:',
			error instanceof Error ? error.message : 'Unknown error',
		);
		process.exit(1);
	}
}

// Run the script
createAdminJwt();
