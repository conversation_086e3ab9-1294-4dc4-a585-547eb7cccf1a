// otp.service.ts
import { ConfigHelper } from '@common/helpers/config.helper';
import { ErrorHelper } from '@common/helpers/error.helper';
import { isEmail } from '@common/utils';
import { TokenType } from '@constants/token-type';
import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { RedisService } from '@shared/services/redis.service';
import { RedisKeyManagerService } from '@shared/services/redis-key-manager.service';
import * as crypto from 'crypto';
import {
	InternalServerErrorException,
	InvalidOtpCodeException,
	OtpAttemptExceedException,
	OtpExpiredException,
	TooManyRequestsException,
	ZaloMissingAccessTokenException,
} from 'exceptions';
import { firstValueFrom } from 'rxjs';

import { OtpMetricsService } from './otp-metrics.service';

@Injectable()
export class OtpService {
	constructor(
		private readonly redisService: RedisService,
		private readonly redisKeyManager: RedisKeyManagerService,
		private readonly httpService: HttpService,
		private readonly otpMetricsService: OtpMetricsService,
		private readonly configHelper: ConfigHelper,
	) {}

	private readonly DEFAULT_OTP_TTL = 5; // 5 minutes
	private readonly DEFAULT_LIMIT_TTL = 5; // 5 minutes for rate limit
	private readonly DEFAULT_MAX_LIMIT_USER = 3; // 3 OTP / user / 5 minutes
	private readonly DEFAULT_MAX_LIMIT_IP = 10; // 10 OTP / IP / 5 minutes
	private readonly DEFAULT_MAX_VERIFY_ATTEMPTS = 3; // 3 verification attempts per OTP

	private MAILER_HOST = process.env.MAILER_API_HOST;

	private async getOtpConfig<T>(key: string, defaultValue: T): Promise<T> {
		return this.configHelper.getConfigWithFallback(key, defaultValue, 'otp');
	}

	async sendOtpMail(email: string, otp: string, expireAfter: number) {
		const payload = {
			to: [email],
			template: 'otp-verification',
			context: {
				name: 'User',
				otp: otp,
				expiryMinutes: expireAfter,
				currentYear: new Date().getFullYear(),
			},
		};

		return await firstValueFrom(
			this.httpService.post(`${this.MAILER_HOST}/mail/send-template`, payload, {
				headers: {
					'Content-Type': 'application/json',
				},
			}),
		);
	}

	async sendOtpZalo(phone: string, otp: string, accessToken: string) {
		const payload = {
			phone: phone,
			otp: otp,
			accessToken: accessToken,
		};

		return await firstValueFrom(
			this.httpService.post(`${this.MAILER_HOST}/zalo/send-otp`, payload, {
				headers: {
					'Content-Type': 'application/json',
				},
			}),
		);
	}

	generateOtp(): string {
		return crypto.randomInt(100000, 999999).toString();
	}

	async sendOtp(
		emailOrPhone: string,
		ipAddress: string,
	): Promise<any> {
		const limitUserKey = this.redisKeyManager.otp.rateLimit(emailOrPhone);
		const limitIpKey = this.redisKeyManager.otp.rateLimitIp(ipAddress);
		const otpKey = this.redisKeyManager.otp.code(emailOrPhone);

		const contactType = ErrorHelper.validateContactFormat(emailOrPhone);

		const pipeline = this.redisService.createPipeline();
		pipeline.incr(limitUserKey);
		pipeline.incr(limitIpKey);
		pipeline.ttl(limitUserKey);
		pipeline.ttl(limitIpKey);

		const results = await pipeline.exec();
		const [userCount, ipCount, userTtl, ipTtl] = results?.map(
			(r: any) => r?.[1] as number,
		) || [0, 0, -1, -1];

		const limitTtl = await this.getOtpConfig(
			'otp_limit_ttl',
			this.DEFAULT_LIMIT_TTL,
		);
		const maxLimitUser = await this.getOtpConfig(
			'otp_max_limit_user',
			this.DEFAULT_MAX_LIMIT_USER,
		);
		const maxLimitIp = await this.getOtpConfig(
			'otp_max_limit_ip',
			this.DEFAULT_MAX_LIMIT_IP,
		);

		if (userTtl === -1) {
			await this.redisService.expire(limitUserKey, limitTtl * 60);
		}
		if (ipTtl === -1) {
			await this.redisService.expire(limitIpKey, limitTtl * 60);
		}

		if ((userCount || 0) > maxLimitUser) {
			throw new TooManyRequestsException(
				`Too many OTP requests for ${emailOrPhone}. Please try again in ${limitTtl} minutes.`,
			);
		}
		if ((ipCount || 0) > maxLimitIp) {
			throw new TooManyRequestsException(
				`Too many OTP requests from this IP. Please try again in ${limitTtl} minutes.`,
			);
		}

		const otp = this.generateOtp();
		const otpTtl = await this.getOtpConfig('otp_ttl', this.DEFAULT_OTP_TTL);

		await this.redisService.set(otpKey, otp, otpTtl * 60);

		try {
			if (contactType === 'email') {
				await this.sendOtpMail(emailOrPhone, otp, otpTtl);
			} else {
				// For Zalo, we need access token
				const accessToken = await this.getOtpConfig('ZALO_ACCESS_TOKEN', null);
				if (!accessToken) {
					throw new ZaloMissingAccessTokenException(
						'Zalo access token is not configured. Please contact support.',
					);
				}
				await this.sendOtpZalo(emailOrPhone, otp, accessToken);
			}
			await this.otpMetricsService.recordOtpSent(
				contactType === 'email' ? 'email' : 'zalo',
			);
		} catch (error) {
			await this.redisService.del(otpKey);
			throw new InternalServerErrorException(
				`Failed to send OTP: ${error instanceof Error ? error.message : 'Unknown error'}`,
			);
		}

		const userRemaining = Math.max(0, maxLimitUser - (userCount || 0));
		const resetTime = userTtl && userTtl > 0 ? userTtl : limitTtl * 60;

		return {
			target: emailOrPhone,
			contactType,
			expiresIn: otpTtl * 60,
			rateLimit: {
				remaining: userRemaining,
				resetTime,
			},
		};
	}

	async verifyOtp(
		emailOrPhone: string,
		otp: string,
	): Promise<any> {
		const otpKey = this.redisKeyManager.otp.code(emailOrPhone);
		const attemptsKey = this.redisKeyManager.otp.verifyAttempts(emailOrPhone);

		const maxAttempts = await this.getOtpConfig(
			'otp_max_attempts',
			this.DEFAULT_MAX_VERIFY_ATTEMPTS,
		);
		const currentAttempts = (await this.redisService.get<number>(attemptsKey)) || 0;

		if (currentAttempts >= maxAttempts) {
			await this.redisService.del(otpKey);
			await this.redisService.del(attemptsKey);
			throw new OtpAttemptExceedException(
				'Too many verification attempts. Please request a new OTP.',
			);
		}

		const storedOtp = await this.redisService.get<string>(otpKey);

		if (!storedOtp) {
			await this.redisService.del(attemptsKey);
			throw new OtpExpiredException(
				'OTP has expired. Please request a new one.',
			);
		}

		if (storedOtp !== otp) {
			await this.redisService.incr(attemptsKey);
			const otpTtl = await this.getOtpConfig('otp_ttl', this.DEFAULT_OTP_TTL);
			await this.redisService.expire(attemptsKey, otpTtl * 60);
			const remainingAttempts = maxAttempts - currentAttempts - 1;
			throw new InvalidOtpCodeException(
				`Invalid OTP code. ${remainingAttempts} attempts remaining.`,
			);
		}

		const type = isEmail(emailOrPhone)
			? TokenType.EMAIL_VERIFICATION
			: TokenType.PHONE_VERIFICATION;

		await this.redisService.del(otpKey);
		await this.redisService.del(attemptsKey);

		await this.otpMetricsService.recordOtpVerified(
			isEmail(emailOrPhone) ? 'email' : 'zalo',
			true,
		);

		return {
			target: emailOrPhone,
			type,
			verified: true,
			verifiedAt: new Date().toISOString(),
		};
	}
}
