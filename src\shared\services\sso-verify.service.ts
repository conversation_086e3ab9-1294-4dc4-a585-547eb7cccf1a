import { UserAccountType } from '@constants/user';
import { SocialInfoDto } from '@modules/auth/dto/social-info.dto';
import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { ApiConfigService } from '@shared/services/api-config.service';
import { InvalidSsoTokenException, SsoTokenVerifyFailedException } from 'exceptions';
import { OAuth2Client } from 'google-auth-library';
import { lastValueFrom } from 'rxjs';

@Injectable()
export class SsoVerifyService {
	private googleClient: OAuth2Client;

	constructor(
		private readonly httpService: HttpService,
		private readonly configService: ApiConfigService,
	) {
		this.googleClient = new OAuth2Client(configService.googleCert.clientID);
	}

	async verifyGoogleToken(idToken: string): Promise<SocialInfoDto> {
		try {
			const ticket = await this.googleClient.verifyIdToken({
				idToken,
				audience: this.configService.googleCert.clientID, // verify it's for your app
			});

			const payload = ticket.getPayload();
			if (!payload) {
				throw new InvalidSsoTokenException('Invalid ID token');
			}

			// return payload; // contains email, sub, aud, iss, exp, iat, etc.
			return new SocialInfoDto({
				socialUid: payload.sub,
				name: payload.name || `${payload.given_name} ${payload.family_name}`,
				email: payload.email,
				avatarUrl: payload.picture,
				provider: UserAccountType.GOOGLE,
				accessToken: idToken,
				refreshToken: null,
			});

		} catch (error) {
			throw new SsoTokenVerifyFailedException(String(error));
		}
	}

	async verifyFacebookToken(userAccessToken: string): Promise<SocialInfoDto> {
		const appId = this.configService.facebookCert.clientID;
		const appSecret = this.configService.facebookCert.clientSecret;

		try {
			// Step 1: Verify token validity
			const debugUrl = `https://graph.facebook.com/debug_token?input_token=${userAccessToken}&access_token=${appId}|${appSecret}`;
			const debugRes = await lastValueFrom(this.httpService.get(debugUrl));
			const data = debugRes.data?.data;

			if (!data?.is_valid) {
				throw new InvalidSsoTokenException('Invalid Facebook access token');
			}

			// Step 2: Fetch user profile
			const profileUrl = `https://graph.facebook.com/${data.user_id}?fields=id,name,email,picture&access_token=${userAccessToken}`;
			const profileRes = await lastValueFrom(this.httpService.get(profileUrl));
			const profile = profileRes.data;

			return new SocialInfoDto({
				socialUid: profile.id,
				name: profile.name || 'Unknown User',
				email: profile.email ?? null,
				avatarUrl: profile.picture?.data?.url ?? null,
				provider: UserAccountType.FACEBOOK,
				accessToken: userAccessToken,
				refreshToken: null,
			});
		} catch (error) {
			throw new SsoTokenVerifyFailedException(String(error));
		}
	}
}
