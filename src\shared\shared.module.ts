import { HttpModule } from '@nestjs/axios';
import { CacheModule } from '@nestjs/cache-manager';
import type { Provider } from '@nestjs/common';
import { Global, Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { TypeOrmModule } from '@nestjs/typeorm';

import { ConfigHelper } from '../common/helpers/config.helper.ts';
import { SysConfigEntity } from '../modules/sys-config/entities/sys-config.entity.ts';
import { SysConfigService } from '../modules/sys-config/sys-config.service.ts';
import { ApiConfigService } from './services/api-config.service.ts';
import { CacheService } from './services/cache.service.ts';
import { OtpService } from './services/otp.service.ts';
import { OtpMetricsService } from './services/otp-metrics.service.ts';
import { RedisService } from './services/redis.service.ts';
import { RedisKeyManagerService } from './services/redis-key-manager.service.ts';
import { SsoVerifyService } from './services/sso-verify.service.ts';
import { TranslationService } from './services/translation.service.ts';
import { ValidatorService } from './services/validator.service.ts';

const providers: Provider[] = [
	ApiConfigService,
	ValidatorService,
	TranslationService,
	CacheService,
	RedisService,
	RedisKeyManagerService,
	OtpService,
	OtpMetricsService,
	ConfigHelper,
	SysConfigService,
	SsoVerifyService,
];

@Global()
@Module({
	providers,
	imports: [
		CacheModule.register(),
		CqrsModule,
		HttpModule,
		TypeOrmModule.forFeature([SysConfigEntity]),
	],
	exports: [...providers, CqrsModule, HttpModule],
})
export class SharedModule {}